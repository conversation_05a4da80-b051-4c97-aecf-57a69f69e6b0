# Multi-Tab Authentication Testing Guide

## Overview
This guide provides comprehensive testing procedures for the multi-tab authentication system implemented for the Botble CMS real-estate plugin. The solution addresses CSRF token mismatches, social login redirect issues, and session synchronization across multiple browser tabs.

## Features Implemented

### 1. CSRF Token Refresh Mechanism
- **Automatic CSRF token refresh** when tokens become stale due to session regeneration
- **AJAX error handling** for 419 responses with automatic retry
- **Graceful error handling** with user-friendly messages
- **Retry limit protection** to prevent infinite loops

### 2. Multi-Tab Session Synchronization
- **Browser storage events** for cross-tab communication
- **Authentication state monitoring** across all tabs
- **Real-time session state updates** when login/logout occurs
- **Automatic UI updates** when authentication state changes

### 3. Enhanced Social Login
- **Improved intended URL storage** with backup mechanisms
- **Cross-tab intended URL preservation** for social login flows
- **Better error handling** for OAuth session issues
- **Fallback mechanisms** for various redirect scenarios

### 4. Authentication State Manager
- **Centralized state management** for authentication across tabs
- **Periodic session monitoring** to detect state changes
- **Event-driven architecture** for real-time updates
- **Cleanup mechanisms** to prevent memory leaks

## Files Modified

### Backend Files
1. `platform/plugins/real-estate/src/Http/Controllers/Fronts/LoginController.php`
   - Added CSRF token refresh endpoint
   - Enhanced error handling for token mismatches
   - Improved AJAX response handling

2. `platform/plugins/real-estate/routes/fronts.php`
   - Added route for CSRF token refresh

3. `platform/plugins/social-login/src/Http/Controllers/SocialLoginController.php`
   - Enhanced intended URL storage with backup mechanisms
   - Improved callback handling for multi-tab scenarios
   - Better session cleanup

### Frontend Files
1. `platform/themes/xmetr/assets/js/auth-state-manager.js` (NEW)
   - Authentication state manager class
   - Cross-tab communication via localStorage
   - CSRF token refresh handling
   - Session monitoring

2. `platform/themes/xmetr/assets/js/auth-modal.js`
   - Enhanced CSRF error handling
   - Integration with auth state manager
   - Improved error messages

3. `platform/themes/xmetr/layouts/base.blade.php`
   - Added auth-state-manager.js inclusion

4. `public/themes/xmetr/js/auth-state-manager.js` (NEW)
   - Public copy of auth state manager

5. `public/themes/xmetr/js/auth-modal.js`
   - Public copy with enhancements

## Testing Scenarios

### Test 1: Basic Multi-Tab Login/Logout
**Objective**: Verify that login/logout actions are synchronized across tabs

**Steps**:
1. Open two browser tabs with the website
2. In Tab 1, click login and authenticate successfully
3. Switch to Tab 2 without refreshing
4. Verify that Tab 2 shows the user as logged in
5. In Tab 2, click logout
6. Switch to Tab 1 without refreshing
7. Verify that Tab 1 shows the user as logged out

**Expected Results**:
- Authentication state should be synchronized across both tabs
- No CSRF token errors should occur
- UI should update automatically in both tabs

### Test 2: CSRF Token Mismatch Handling
**Objective**: Test CSRF token refresh mechanism

**Steps**:
1. Open two browser tabs with the website
2. In Tab 1, open login modal but don't submit
3. In Tab 2, login successfully (this regenerates session)
4. In Tab 1, try to submit the login form
5. Observe the behavior

**Expected Results**:
- CSRF token should be automatically refreshed
- User should see a message about session expiry
- Form should be ready for resubmission with new token
- No hard errors or page crashes

### Test 3: Social Login Cross-Tab Redirect
**Objective**: Verify social login redirects work correctly across tabs

**Steps**:
1. Open a specific property page in Tab 1
2. Open the same property page in Tab 2
3. In Tab 1, login successfully via Google
4. In Tab 2, click "Login with Google" without refreshing
5. Complete the Google authentication

**Expected Results**:
- User should be redirected back to the property page
- No redirect to homepage should occur
- Authentication state should sync across tabs

### Test 4: Session Monitoring
**Objective**: Test periodic session state checking

**Steps**:
1. Open two browser tabs
2. Login in Tab 1
3. Wait for 30+ seconds (session check interval)
4. Switch between tabs and observe console logs
5. Logout in one tab and observe the other

**Expected Results**:
- Console should show periodic session checks
- Authentication state should be detected and synchronized
- No unnecessary API calls or errors

### Test 5: Error Recovery
**Objective**: Test error handling and recovery mechanisms

**Steps**:
1. Open login modal in multiple tabs
2. Simulate network issues during login
3. Try various error scenarios (invalid credentials, server errors)
4. Test CSRF token refresh failures

**Expected Results**:
- Graceful error handling with user-friendly messages
- No infinite retry loops
- Proper cleanup of failed requests
- System should recover when network is restored

## Browser Console Monitoring

During testing, monitor the browser console for these messages:

### Normal Operation
- `AuthStateManager initialized`
- `Auth state changed: logged in/logged out`
- `CSRF token updated`
- `Intended URL stored in session`

### Error Handling
- `CSRF token mismatch detected, attempting refresh...`
- `CSRF token refreshed successfully`
- `Max retry attempts reached for CSRF token refresh`
- `Failed to refresh CSRF token`

### Cross-Tab Communication
- `Updating auth UI for state: true/false`
- Storage events in browser dev tools

## Performance Considerations

### Monitoring Points
1. **Session Check Frequency**: Default 30 seconds, adjustable in auth-state-manager.js
2. **localStorage Usage**: Monitor for excessive storage operations
3. **AJAX Requests**: Watch for unnecessary token refresh requests
4. **Memory Usage**: Ensure proper cleanup of event listeners

### Optimization Settings
- Session check interval can be adjusted based on requirements
- CSRF refresh retry limit is set to 2 attempts
- Storage events are filtered to prevent unnecessary processing

## Troubleshooting

### Common Issues
1. **CSRF Token Not Updating**: Check meta tag presence and jQuery setup
2. **Cross-Tab Not Working**: Verify localStorage support and event listeners
3. **Social Login Redirects**: Check intended URL storage and retrieval logic
4. **Console Errors**: Monitor for JavaScript errors and AJAX failures

### Debug Mode
Enable debug logging by setting `log_oauth_errors` to `true` in social-login config.

## Browser Compatibility
- Modern browsers with ES6 support
- localStorage support required
- jQuery-based implementation for compatibility
- Graceful degradation for older browsers

## Security Considerations
- CSRF tokens are properly validated and refreshed
- Intended URLs are validated before storage
- Session data is cleaned up after use
- No sensitive data stored in localStorage
