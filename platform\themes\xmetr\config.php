<?php

use Xmetr\Base\Facades\BaseHelper;
use Xmetr\Shortcode\View\View;
use Xmetr\Theme\Theme;

return [

    'inherit' => null, //default

    'events' => [
        'beforeRenderTheme' => function (Theme $theme): void {
            $version = get_cms_version() . '.26';

           // $boostrapCss = BaseHelper::isRtlEnabled() ? 'bootstrap.rtl.min.css' : 'bootstrap.min.css';

            $theme->asset()->usePath()->add('bootstrap', "/css/bootstrap.min.css");
            $theme->asset()->usePath()->add('animate', 'css/animate.min.css');
            $theme->asset()->usePath()->add('swiper', 'plugins/swiper/swiper-bundle.min.css');
            $theme->asset()->usePath()->add('jquery-ui', 'css/jquery-ui.min.css', version: $version);
            $theme->asset()->usePath()->add('ace-responsive-menu-css', 'css/ace-responsive-menu.css', version: $version);
            $theme->asset()->usePath()->add('menu', 'css/menu.css', version: $version);
            $theme->asset()->usePath()->add('fontawesome', 'css/fontawesome.css', version: $version);
            $theme->asset()->usePath()->add('flaticon', 'css/flaticon.css', version: $version);
            $theme->asset()->usePath()->add('bootstrap-select', 'css/bootstrap-select.min.css', version: $version);
            $theme->asset()->usePath()->add('slider', 'css/slider.css', version: $version);
            $theme->asset()->usePath()->add('ud-custom-spacing', 'css/ud-custom-spacing.css', version: $version);
            $theme->asset()->usePath()->add('x-metr-css', 'css/x-metr.css', version: $version);

            $theme->asset()->usePath()->add('style', 'css/style.css', version: $version);
            $theme->asset()->usePath()->add('main', 'css/main.css', version: $version);

            $theme->asset()->usePath()->add('responsive', 'css/responsive.css', version: $version);

            $theme->asset()->container('footer')->usePath()->add('jquery', 'js/jquery-3.6.4.min.js', version: $version);
            $theme->asset()->container('footer')->usePath()->add('jquery-migrate', 'js/jquery-migrate-3.0.0.min.js', version: $version);
            $theme->asset()->container('footer')->usePath()->add('popper', 'js/popper.min.js', version: $version);
            $theme->asset()->container('footer')->usePath()->add('bootstrap-js', 'js/bootstrap.min.js', version: $version);
            $theme->asset()->container('footer')->usePath()->add('bootstrap-select-js', 'js/bootstrap-select.min.js', version: $version);
            $theme->asset()->container('footer')->usePath()->add('jquery.mmenu.all', 'js/jquery.mmenu.all.js', version: $version);
            $theme->asset()->container('footer')->usePath()->add('ace-responsive-menu', 'js/ace-responsive-menu.js', version: $version);
            $theme->asset()->container('footer')->usePath()->add('jquery-scrolltofixed', 'js/jquery-scrolltofixed-min.js', version: $version);
            $theme->asset()->container('footer')->usePath()->add('owl', 'js/owl.js', version: $version);
            $theme->asset()->container('footer')->usePath()->add('wow', 'js/wow.min.js', version: $version);
            // $theme->asset()->container('footer')->usePath()->add('swiper-js', 'js/swiper.js', version: $version);
            $theme->asset()->container('footer')->usePath()->add('swiper-js', 'plugins/swiper/swiper-bundle.min.js', version: $version);
            $theme->asset()->container('footer')->usePath()->add('parallax-js', 'js/parallax.js', version: $version);
            $theme->asset()->container('footer')->usePath()->add('pricing-slider', 'js/pricing-slider.js', version: $version);
            $theme->asset()->container('footer')->usePath()->add('script', 'js/script.js', version: $version);
            $theme->asset()->container('footer')->usePath()->add('script-main', 'js/script-main.js', version: $version);
            $theme->asset()->container('footer')->usePath()->add('x-metr', 'js/x-metr.js', version: $version);
            $theme->asset()->container('footer')->usePath()->add('xmetr-map', 'js/filter-map.js', version: $version);
            $theme->asset()->container('footer')->usePath()->add('auth-modal', 'js/auth-modal.js', ['jquery'], version: $version);

            $theme->asset()->add('slick-css', $theme->asset()->url('plugins/slick.css'), version: $version);
            $theme->asset()->add('slick-theme-css', $theme->asset()->url('plugins/slick-theme.css'), version: $version);
            $theme->asset()->container('footer')->add('slick-js', $theme->asset()->url('plugins/slick.js'), version: $version);
            $theme->asset()->container('footer')->add('main-xmetr', $theme->asset()->url('js/main.js'), version: $version);
            $theme->asset()->container('footer')->add('home-js', $theme->asset()->url('js/page/home.js'), version: $version);


            $theme->asset()
                ->usePath(false)
                ->add(
                    'social-login-css',
                    asset('vendor/core/plugins/social-login/css/social-login.css'),
                    [],
                    [],
                    '1.1.0'
                );

            if (function_exists('shortcode')) {
                $theme->composer([
                    'page',
                    'post',
                    'career.career',
                    'real-estate.project',
                    'real-estate.property',
                ], function (View $view): void {
                    $view->withShortcodes();
                });
            }
        },
    ],
];
