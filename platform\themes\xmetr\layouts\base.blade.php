<!DOCTYPE html>
<html {!! Theme::htmlAttributes() !!}>
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=5, user-scalable=1" name="viewport"/>
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <style>
            :root {
                --title-font-family: var(--primary-font);
                --body-font-family: var(--primary-font);
                --primary-color: {{ theme_option('primary_color', '#db1d23') }};
                --hover-color: {{ theme_option('hover_color', '#cd380f') }};
                --top-header-background-color: {{ theme_option('top_header_background_color', '#f7f7f7') }};
                --top-header-text-color: {{ theme_option('top_header_text_color', '#161e2d') }};
                --main-header-background-color: {{ theme_option('main_header_background_color', '#ffffff') }};
                --main-header-text-color: {{ theme_option('main_header_text_color', '#161e2d') }};
                --main-header-border-color: {{ theme_option('main_header_border_color', '#e4e4e4') }};
            }
            .login-options-title{
                display: none;
            }
            .login-options .social-login-basic {
                padding: 0 !important;
            }
            .login-options .social-login-basic .social-login{
                justify-content: center;
                font-size: 14px !important;
                font-weight: 600 !important;
            }

        </style>
        <script  src="https://cdn.tailwindcss.com"></script>

        {!! Theme::header() !!}

        <!-- Hotjar Tracking Code for xmetr.com -->
        <script>
            (function(h,o,t,j,a,r){
                h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                h._hjSettings={hjid:6417422,hjsv:6};
                a=o.getElementsByTagName('head')[0];
                r=o.createElement('script');r.async=1;
                r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                a.appendChild(r);
            })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
        </script>

    </head>

    <body {!! Theme::bodyAttributes() !!}>
        {!! apply_filters(THEME_FRONT_BODY, null) !!}

        <div id="wrapper">
            <div class="clearfix">
                @yield('content')
            </div>
        </div>

        {!! Theme::footer() !!}

        <!-- Authentication State Manager for multi-tab session synchronization -->
        <script src="{{ Theme::asset()->url('js/auth-state-manager.js') }}"></script>

        <script>
            Fancybox.bind("[data-fancybox]", {
            // Your custom options
            });
        </script>
    </body>
</html>
