@php
    Theme::set('breadcrumbEnabled', 'no');
    Theme::layout('full-width');
    SeoHelper::meta()->addMeta('robots', 'noindex, nofollow');
@endphp
<style>
    .agent-grid-area {
        display: none;
    }
</style>
<section class="agent-single pt60">
    <div class="cta-agent bgc-thm-light mx-auto maxw1600 pt60 pb60 bdrs12 position-relative mx20-lg">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-xl-7">
                    <div class="agent-single d-sm-flex align-items-center">
                        <div class="single-img mb30-sm">
                            {{-- <img
                                src="{{ RvMedia::getImageUrl($account->avatar_url, 'thumb') }}"
                                style="border-radius: 50%;" alt=""> --}}
{{ RvMedia::image($account->avatar->url ?: $account->avatar_url, $account->name, 'thumb', false, ['class'=> 'object-cover shrink-0 rounded-[100%]', 'width'=> '150px', 'height'=>'150px']) }}

                        </div>
                        <div class="single-contant ml30 ml0-xs">
                            <h2 class="title mb-0">{{ $account->name }}</h2>
                            <div class="flex items-center gap-[10px]">
                                @if ($account->is_verified)
                                    <svg width="20" height="20" class="max-[430px]:w-[16px] max-[430px]:h-[16px]"
                                        viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M19.5562 8.74032L18.1961 7.16071C17.9361 6.86078 17.7261 6.30092 17.7261 5.90102V4.20145C17.7261 3.14171 16.8561 2.27193 15.796 2.27193H14.0959C13.7059 2.27193 13.1358 2.06198 12.8358 1.80205L11.2557 0.442389C10.5657 -0.147463 9.43561 -0.147463 8.73556 0.442389L7.16543 1.81205C6.86541 2.06198 6.29538 2.27193 5.90535 2.27193H4.17525C3.11519 2.27193 2.24514 3.14171 2.24514 4.20145V5.91102C2.24514 6.30092 2.03512 6.86078 1.78511 7.16071L0.435026 8.75032C-0.145009 9.44015 -0.145009 10.5599 0.435026 11.2497L1.78511 12.8393C2.03512 13.1392 2.24514 13.6991 2.24514 14.089V15.7986C2.24514 16.8583 3.11519 17.7281 4.17525 17.7281H5.90535C6.29538 17.7281 6.86541 17.938 7.16543 18.198L8.74557 19.5576C9.43561 20.1475 10.5657 20.1475 11.2657 19.5576L12.8458 18.198C13.1458 17.938 13.7059 17.7281 14.1059 17.7281H15.806C16.8661 17.7281 17.7361 16.8583 17.7361 15.7986V14.099C17.7361 13.7091 17.9461 13.1392 18.2061 12.8393L19.5662 11.2597C20.1463 10.5699 20.1462 9.43015 19.5562 8.74032ZM14.1559 8.11048L9.3256 12.9393C9.18559 13.0792 8.99558 13.1592 8.79557 13.1592C8.59556 13.1592 8.40554 13.0792 8.26554 12.9393L5.84535 10.5199C5.55533 10.2299 5.55533 9.75007 5.84535 9.46014C6.13537 9.17021 6.6154 9.17021 6.90542 9.46014L8.79557 11.3497L13.0958 7.05073C13.3858 6.76081 13.8659 6.76081 14.1559 7.05073C14.4459 7.34066 14.4459 7.82054 14.1559 8.11048Z"
                                            fill="#0071FF" />
                                    </svg>
                                @endif
                                <p class="fz15">{{ $account->with_xmetr }}</p>
                            </div>

                            @if ($account->spokenLanguages->isNotEmpty())
                                <div class="w-full flex items-start gap-[8px]">
                                    <p class="shrink-0 text-[13px] text-[#717171]">{{ __('Host Speak') }}</p>
                                    @foreach ($account->spokenLanguages as $language)
                                        <div class="flag-container flex items-center gap-[8px]">
                                            <span class="language-flag">{!! language_flag($language->flag, $language->name, 20) !!}</span>
                                        </div>
                                    @endforeach
                                </div>
                            @endif

                            <div class="flex items-center gap-[10px]">
                                <p class="text-[15px] text-[#717171] max-[430px]:text-[13px]">
                                    {{ $account->account_type->label() }}</p>
                            </div>


                            <div class="flex items-center gap-[10px]">
                                @if ($whatsapp = $account->getMetaData('whatsapp', true))
                                    <a @if (auth('account')->check()) href="https://wa.me/{{ $whatsapp }}" target="_blank"  @else  href="#modalSignin" role="button"  data-bs-toggle="modal" @endif
                                        class="h-[50px] rounded-[10px] bg-[#7DC678] flex justify-center items-center gap-[8px] px-[33px] w-full max-[500px]:px-[8px]">
                                        <svg width="20" height="20"
                                            class="max-[430px]:w-[16px] max-[430px]:h-[16px]" viewBox="0 0 20 20"
                                            fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M4.40471 6.15476C4.5051 5.36085 5.53688 4.28185 6.35572 4.40158L6.35441 4.40027C7.1513 4.5517 7.78212 5.92327 8.13783 6.54C8.38986 6.98747 8.2262 7.44084 7.99112 7.63214C7.67399 7.88775 7.17807 8.24043 7.28212 8.59455C7.4671 9.22407 9.66331 11.4203 10.7435 12.0446C11.1523 12.2809 11.4488 11.6576 11.702 11.3383C11.886 11.0927 12.3397 10.9459 12.7861 11.1884C13.4529 11.5766 14.081 12.0279 14.6619 12.5359C14.952 12.7784 15.02 13.1367 14.819 13.5155C14.465 14.1825 13.4411 15.0621 12.6978 14.8833C11.3995 14.5712 6.14938 12.5359 4.46298 6.63958C4.36814 6.36064 4.39202 6.25518 4.40471 6.15476Z"
                                                fill="white" />
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M9.66331 19.3266C8.5884 19.3266 7.99367 19.2113 7.02786 18.8874L5.17816 19.8122C4.00994 20.3964 2.63545 19.5469 2.63545 18.2407V16.2519C0.74368 14.4879 0 12.454 0 9.66331C0 4.32641 4.32641 0 9.66331 0C15.0002 0 19.3266 4.32641 19.3266 9.66331C19.3266 15.0002 15.0002 19.3266 9.66331 19.3266ZM4.39241 15.4879L3.83365 14.9669C2.36388 13.5965 1.75697 12.0643 1.75697 9.66331C1.75697 5.29676 5.29676 1.75697 9.66331 1.75697C14.0299 1.75697 17.5697 5.29676 17.5697 9.66331C17.5697 14.0299 14.0299 17.5697 9.66331 17.5697C8.79739 17.5697 8.39127 17.4915 7.58653 17.2216L6.89475 16.9896L4.39241 18.2407V15.4879Z"
                                                fill="white" />
                                        </svg>

                                        <p class="text-white text-[15px] font-bold">WhatsApp</p>
                                    </a>
                                @endif

                                @if ($telegram = $account->getMetaData('telegram', true))
                                    <a @if (auth('account')->check()) href="https://t.me/{{ $telegram }}" target="_blank"  @else  href="#modalSignin" role="button"  data-bs-toggle="modal" @endif
                                        class="h-[50px] rounded-[10px] bg-[#4DA0D9] flex justify-center items-center gap-[8px] px-[33px] w-full max-[500px]:px-[8px]">
                                        <svg width="20" height="20"
                                            class="max-[430px]:w-[16px] max-[430px]:h-[16px]" viewBox="0 0 20 19"
                                            fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M19.9615 2.17749C20.2493 0.774406 18.8712 -0.391584 17.5352 0.124619L1.15816 6.45211C-0.333844 7.02856 -0.400324 9.11491 1.05194 9.78514L4.6184 11.4312L6.31501 17.3693C6.40314 17.6778 6.6479 17.9166 6.95843 17.9971C7.26895 18.0775 7.59888 17.9877 7.82571 17.7608L10.4392 15.1473L14.1001 17.8931C15.1628 18.6901 16.6934 18.1096 16.9603 16.8083L19.9615 2.17749ZM1.81073 8.14112L18.1878 1.81364L15.1865 16.4445L10.8974 13.2276C10.537 12.9573 10.0327 12.9932 9.71406 13.3117L8.59469 14.4311L8.93103 12.5814L15.5212 5.99131C15.8419 5.67059 15.8758 5.16203 15.6005 4.80159C15.3251 4.44113 14.8257 4.34003 14.4318 4.56507L5.33067 9.76568L1.81073 8.14112ZM6.44037 11.217L6.98935 13.1386L7.20013 11.9793C7.23307 11.7981 7.32048 11.6312 7.4507 11.5011L9.46048 9.49136L6.44037 11.217Z"
                                                fill="white" />
                                        </svg>

                                        <p class="text-white text-[15px] font-bold">Telegram</p>
                                    </a>
                                @endif

                                @if ($account->phone)
                                    <a @if (auth('account')->check()) href="tel:{{ $account->phone }}"  target="_blank"  @else href="#modalSignin"  role="button"  data-bs-toggle="modal" @endif
                                        class="w-[50px] h-[50px] shrink-0 rounded-[10px] bg-[#212329] hover:bg-[#3e424d] flex justify-center items-center gap-[8px] px-[8px]">
                                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M15.6202 8.75169C15.1902 8.75169 14.8501 8.40168 14.8501 7.98164C14.8501 7.61164 14.4801 6.84163 13.8601 6.17162C13.2501 5.52162 12.5801 5.14161 12.0201 5.14161C11.5901 5.14161 11.2501 4.79161 11.2501 4.3716C11.2501 3.9516 11.6001 3.6016 12.0201 3.6016C13.0201 3.6016 14.0701 4.1416 14.9901 5.11161C15.8502 6.02162 16.4002 7.15163 16.4002 7.97164C16.4002 8.40168 16.0502 8.75169 15.6202 8.75169Z"
                                                fill="white" />
                                            <path
                                                d="M19.23 8.75009C18.8 8.75009 18.46 8.40008 18.46 7.98008C18.46 4.43004 15.57 1.55002 12.0299 1.55002C11.5999 1.55002 11.2599 1.20001 11.2599 0.780008C11.2599 0.360004 11.5999 0 12.0199 0C16.42 0 20 3.58003 20 7.98008C20 8.40008 19.65 8.75009 19.23 8.75009Z"
                                                fill="white" />
                                            <path
                                                d="M9.05009 12.9501L7.20007 14.8001C6.81007 15.1902 6.19006 15.1902 5.79006 14.8101C5.68006 14.7001 5.57006 14.6001 5.46005 14.4901C4.43004 13.4501 3.50004 12.3601 2.67003 11.2201C1.85002 10.0801 1.19001 8.94009 0.710007 7.81008C0.240002 6.67007 0 5.58006 0 4.54005C0 3.86004 0.120001 3.21003 0.360004 2.61003C0.600006 2.00002 0.98001 1.44001 1.51002 0.940009C2.15002 0.310003 2.85003 0 3.59004 0C3.87004 0 4.15004 0.0600007 4.40004 0.180002C4.66005 0.300003 4.89005 0.480005 5.07005 0.740007L7.39007 4.01004C7.57008 4.26004 7.70008 4.49005 7.79008 4.71005C7.88008 4.92005 7.93008 5.13005 7.93008 5.32005C7.93008 5.56006 7.86008 5.80006 7.72008 6.03006C7.59008 6.26006 7.40007 6.50006 7.16007 6.74007L6.40006 7.53008C6.29006 7.64008 6.24006 7.77008 6.24006 7.93008C6.24006 8.01008 6.25006 8.08008 6.27006 8.16008C6.30006 8.24008 6.33006 8.30008 6.35006 8.36008C6.53007 8.69009 6.84007 9.12009 7.28007 9.6401C7.73008 10.1601 8.21008 10.6901 8.73009 11.2201C8.83009 11.3201 8.94009 11.4201 9.04009 11.5201C9.44009 11.9101 9.45009 12.5501 9.05009 12.9501Z"
                                                fill="white" />
                                            <path
                                                d="M19.9698 16.3293C19.9698 16.6093 19.9198 16.8993 19.8198 17.1793C19.7898 17.2593 19.7598 17.3393 19.7198 17.4193C19.5498 17.7793 19.3298 18.1193 19.0398 18.4393C18.5498 18.9793 18.0098 19.3693 17.3998 19.6193C17.3898 19.6193 17.3798 19.6293 17.3698 19.6293C16.7798 19.8693 16.1398 19.9993 15.4498 19.9993C14.4297 19.9993 13.3397 19.7593 12.1897 19.2693C11.0397 18.7793 9.8897 18.1193 8.74969 17.2893C8.35968 16.9993 7.96969 16.7093 7.59969 16.3993L10.8697 13.1292C11.1497 13.3392 11.3997 13.4992 11.6097 13.6092C11.6597 13.6292 11.7197 13.6592 11.7897 13.6892C11.8697 13.7192 11.9497 13.7292 12.0397 13.7292C12.2097 13.7292 12.3397 13.6692 12.4497 13.5592L13.2097 12.8092C13.4597 12.5592 13.6997 12.3692 13.9297 12.2492C14.1597 12.1092 14.3897 12.0392 14.6397 12.0392C14.8297 12.0392 15.0297 12.0792 15.2498 12.1692C15.4698 12.2592 15.6998 12.3892 15.9498 12.5592L19.2598 14.9092C19.5198 15.0893 19.6998 15.2993 19.8098 15.5493C19.9098 15.7993 19.9698 16.0493 19.9698 16.3293Z"
                                                fill="white" />
                                        </svg>
                                    </a>
                                @endif
                            </div>




                        </div>
                    </div>
                    <div class="img-box-11 position-relative d-none d-xl-block">
                        <img class="img-1 spin-right" src="{{ Theme::asset()->url('images/about/element-3.png') }}"
                            alt="">
                        <img class="img-2 bounce-x" src="{{ Theme::asset()->url('images/about/element-5.png') }}"
                            alt="">
                        <img class="img-3 bounce-y" src="{{ Theme::asset()->url('images/about/element-7.png') }}"
                            alt="">
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- <div class="agent-header">
        <div class="agent-avatar">
            {{ RvMedia::image($account->avatar_url, $account->name, 'medium-square') }}
        </div>
        <div class="agent-info">
            <h2 class="agent-name">{{ $account->name }}</h2>
            @if ($account->company)
                <p class="agent-company">{!! BaseHelper::clean(__('Company Agent at :company', ['company' => "<strong>$account->company</strong>"])) !!}</p>
            @endif
            <div class="agent-contact-info">
                @if ($account->phone && !setting('real_estate_hide_agency_phone', false))
                    <a href="tel:{{ $account->phone }}" class="agent-info-item">
                        <x-core::icon name="ti ti-phone" />
                        {{ $account->phone }}
                    </a>
                @endif
                @if ($account->email && !setting('real_estate_hide_agency_email', false))
                    <a href="mailto:{{ $account->email }}" class="agent-info-item">
                        <x-core::icon name="ti ti-mail" />
                        {{ $account->email }}
                    </a>
                @endif
                <div class="agent-info-item">
                    <x-core::icon name="ti ti-calendar" />
                    {{ __('Joined') }} {{ $account->created_at->diffForHumans() }}
                </div>
            </div>

            {!! Theme::partial('shortcodes.agents.partials.social-links', compact('account')) !!}
        </div>
    </div> --}}

    {{-- @if ($account->description)
        <div class="agent-about-section">
            <h5>{{ __('About Agent') }}</h5>
            <p class="agent-description">{!! BaseHelper::clean($account->description) !!}</p>
        </div>
    @endif --}}

    <div class="container pt60">
        <div class="row fadeInUp">
            <div class="col-lg-6 mx-auto">
                <div class="main-title2 text-center">
                    {{-- <h2 class="title">{{ __('Listings') }} ({{ $account->properties->where('moderation_status', 'approved')->count() }})</h2> --}}
                    <h2 class="title text-capitalize">{{ __('listings') }} ({{ $account->properties_count }})</h2>
                </div>
            </div>
        </div>

        @if ($properties->isNotEmpty())
            @php
                $rented_overlay = true;
            @endphp
            {{-- <h5>{{ __('Properties by this agent') }}</h5> --}}
            @include(Theme::getThemeNamespace('views.real-estate.properties.index', ['rented_overlay' => $rented_overlay]))
        @endif

    </div>



    {!! apply_filters('real_estate_agent_details', null, $account) !!}
</section>
