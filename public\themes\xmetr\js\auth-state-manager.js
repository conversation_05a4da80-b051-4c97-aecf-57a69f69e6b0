/**
 * Authentication State Manager
 * Handles multi-tab authentication synchronization, CSRF token refresh,
 * and session state management for the real-estate plugin
 */

class AuthStateManager {
    constructor() {
        this.storageKey = 'xmetr_auth_state';
        this.csrfTokenKey = 'xmetr_csrf_token';
        this.lastActivityKey = 'xmetr_last_activity';
        this.sessionCheckInterval = 30000; // 30 seconds
        this.csrfRefreshUrl = '/account/refresh-csrf-token';
        this.isInitialized = false;
        
        this.init();
    }

    init() {
        if (this.isInitialized) return;
        
        // Listen for storage events (cross-tab communication)
        window.addEventListener('storage', this.handleStorageChange.bind(this));
        
        // Listen for focus events to check session state
        window.addEventListener('focus', this.handleWindowFocus.bind(this));
        
        // Set up periodic session checks
        this.setupSessionMonitoring();
        
        // Set up AJAX error handling for CSRF token mismatches
        this.setupAjaxErrorHandling();
        
        // Initialize current state
        this.updateLastActivity();
        this.syncAuthState();
        
        this.isInitialized = true;
        console.log('AuthStateManager initialized');
    }

    /**
     * Handle storage events from other tabs
     */
    handleStorageChange(event) {
        if (event.key === this.storageKey) {
            const newState = JSON.parse(event.newValue || '{}');
            const oldState = JSON.parse(event.oldValue || '{}');
            
            // Check if authentication state changed
            if (newState.isAuthenticated !== oldState.isAuthenticated) {
                this.handleAuthStateChange(newState.isAuthenticated);
            }
            
            // Check if CSRF token changed
            if (newState.csrfToken && newState.csrfToken !== oldState.csrfToken) {
                this.updateCsrfToken(newState.csrfToken);
            }
        }
    }

    /**
     * Handle window focus events
     */
    handleWindowFocus() {
        // Check if we need to refresh the page due to auth state changes
        this.checkAuthState();
    }

    /**
     * Set up periodic session monitoring
     */
    setupSessionMonitoring() {
        setInterval(() => {
            this.checkAuthState();
        }, this.sessionCheckInterval);
    }

    /**
     * Set up AJAX error handling for CSRF token mismatches
     */
    setupAjaxErrorHandling() {
        const self = this;

        // Override jQuery's AJAX error handling
        $(document).ajaxError(function(event, xhr, settings) {
            if (xhr.status === 419) { // CSRF token mismatch
                console.log('CSRF token mismatch detected, attempting refresh...');

                // Prevent infinite retry loops
                if (settings.retryCount && settings.retryCount >= 2) {
                    console.error('Max retry attempts reached for CSRF token refresh');
                    self.showCsrfError();
                    return;
                }

                self.refreshCsrfToken().then(() => {
                    // Retry the original request with retry counter
                    settings.retryCount = (settings.retryCount || 0) + 1;
                    self.retryAjaxRequest(settings);
                }).catch(() => {
                    // If CSRF refresh fails, show error message
                    self.showCsrfError();
                });
            }
        });
    }

    /**
     * Check current authentication state
     */
    checkAuthState() {
        const currentState = this.getCurrentAuthState();
        const storedState = this.getStoredAuthState();
        
        // If states don't match, update stored state and handle changes
        if (currentState.isAuthenticated !== storedState.isAuthenticated) {
            this.setStoredAuthState(currentState);
            this.handleAuthStateChange(currentState.isAuthenticated);
        }
        
        this.updateLastActivity();
    }

    /**
     * Get current authentication state from DOM
     */
    getCurrentAuthState() {
        // Check if user is authenticated by looking for logout forms or user menu
        const isAuthenticated = document.getElementById('logout-form') !== null ||
                               document.getElementById('logout-form-mob') !== null ||
                               document.querySelector('.user-menu') !== null;
        
        return {
            isAuthenticated: isAuthenticated,
            csrfToken: this.getCurrentCsrfToken(),
            timestamp: Date.now()
        };
    }

    /**
     * Get current CSRF token from meta tag
     */
    getCurrentCsrfToken() {
        const metaTag = document.querySelector('meta[name="csrf-token"]');
        return metaTag ? metaTag.getAttribute('content') : null;
    }

    /**
     * Get stored authentication state from localStorage
     */
    getStoredAuthState() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            return stored ? JSON.parse(stored) : { isAuthenticated: false, csrfToken: null };
        } catch (e) {
            return { isAuthenticated: false, csrfToken: null };
        }
    }

    /**
     * Set authentication state in localStorage
     */
    setStoredAuthState(state) {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(state));
        } catch (e) {
            console.warn('Failed to store auth state:', e);
        }
    }

    /**
     * Handle authentication state changes
     */
    handleAuthStateChange(isAuthenticated) {
        console.log('Auth state changed:', isAuthenticated ? 'logged in' : 'logged out');
        
        // Trigger custom events for other scripts to listen to
        const event = new CustomEvent('authStateChanged', {
            detail: { isAuthenticated: isAuthenticated }
        });
        document.dispatchEvent(event);
        
        // Update UI elements if needed
        this.updateAuthUI(isAuthenticated);
    }

    /**
     * Update authentication-related UI elements
     */
    updateAuthUI(isAuthenticated) {
        // This can be extended to update specific UI elements
        // For now, we'll just log the state change
        console.log('Updating auth UI for state:', isAuthenticated);
    }

    /**
     * Refresh CSRF token via AJAX
     */
    refreshCsrfToken() {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: this.csrfRefreshUrl,
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: (response) => {
                    if (response.success && response.csrf_token) {
                        this.updateCsrfToken(response.csrf_token);
                        console.log('CSRF token refreshed successfully');
                        resolve(response.csrf_token);
                    } else {
                        reject('Invalid response from CSRF refresh endpoint');
                    }
                },
                error: (xhr, status, error) => {
                    console.error('Failed to refresh CSRF token:', error);
                    reject(error);
                }
            });
        });
    }

    /**
     * Update CSRF token in meta tag and jQuery setup
     */
    updateCsrfToken(newToken) {
        // Update meta tag
        const metaTag = document.querySelector('meta[name="csrf-token"]');
        if (metaTag) {
            metaTag.setAttribute('content', newToken);
        }
        
        // Update jQuery AJAX setup
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': newToken
            }
        });
        
        // Update stored state
        const currentState = this.getStoredAuthState();
        currentState.csrfToken = newToken;
        this.setStoredAuthState(currentState);
        
        console.log('CSRF token updated');
    }

    /**
     * Retry failed AJAX request with new CSRF token
     */
    retryAjaxRequest(originalSettings) {
        // Update the CSRF token in the request data if it exists
        if (originalSettings.data && typeof originalSettings.data === 'object') {
            originalSettings.data._token = this.getCurrentCsrfToken();
        }
        
        // Retry the request
        $.ajax(originalSettings);
    }

    /**
     * Show CSRF error message to user
     */
    showCsrfError() {
        // You can customize this to show a proper notification
        alert('Session expired. Please refresh the page and try again.');
    }

    /**
     * Update last activity timestamp
     */
    updateLastActivity() {
        try {
            localStorage.setItem(this.lastActivityKey, Date.now().toString());
        } catch (e) {
            console.warn('Failed to update last activity:', e);
        }
    }

    /**
     * Sync authentication state across tabs
     */
    syncAuthState() {
        const currentState = this.getCurrentAuthState();
        this.setStoredAuthState(currentState);
    }

    /**
     * Manually trigger authentication state sync
     */
    triggerSync() {
        this.syncAuthState();
    }

    /**
     * Clean up event listeners
     */
    destroy() {
        window.removeEventListener('storage', this.handleStorageChange.bind(this));
        window.removeEventListener('focus', this.handleWindowFocus.bind(this));
        this.isInitialized = false;
    }
}

// Initialize the auth state manager when DOM is ready
$(document).ready(function() {
    window.authStateManager = new AuthStateManager();
    
    // Sync state when login/logout actions occur
    $(document).on('submit', '#logout-form, #logout-form-mob', function() {
        setTimeout(() => {
            window.authStateManager.triggerSync();
        }, 100);
    });
});
