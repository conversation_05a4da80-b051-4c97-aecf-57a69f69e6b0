{"__meta": {"id": "01K2X4K4HG8VNH70CDADBMXTD8", "datetime": "2025-08-17 23:07:20", "utime": **********.497464, "method": "GET", "uri": "/en/rent-properties", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1755472028.169867, "end": **********.497485, "duration": 12.327617883682251, "duration_str": "12.33s", "measures": [{"label": "Booting", "start": 1755472028.169867, "relative_start": 0, "end": **********.589599, "relative_end": **********.589599, "duration": 1.***************, "duration_str": "1.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.589618, "relative_start": 1.****************, "end": **********.497488, "relative_end": 3.0994415283203125e-06, "duration": 10.***************, "duration_str": "10.91s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.623348, "relative_start": 1.****************, "end": **********.675031, "relative_end": **********.675031, "duration": 0.*****************, "duration_str": "51.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: theme.xmetr::views.page", "start": **********.992036, "relative_start": 1.****************, "end": **********.992036, "relative_end": **********.992036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties", "start": **********.188422, "relative_start": 2.***************, "end": **********.188422, "relative_end": **********.188422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.partials.listing", "start": **********.207432, "relative_start": 2.037564992904663, "end": **********.207432, "relative_end": **********.207432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.listing-layouts.xmetr", "start": **********.208319, "relative_start": 2.038451910018921, "end": **********.208319, "relative_end": **********.208319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.partials.map", "start": **********.41368, "relative_start": 2.2438130378723145, "end": **********.41368, "relative_end": **********.41368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.index", "start": **********.416032, "relative_start": 2.2461650371551514, "end": **********.416032, "relative_end": **********.416032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.grid", "start": **********.417024, "relative_start": 2.247156858444214, "end": **********.417024, "relative_end": **********.417024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.417857, "relative_start": 2.2479898929595947, "end": **********.417857, "relative_end": **********.417857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.935081, "relative_start": 2.765213966369629, "end": **********.935081, "relative_end": **********.935081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": 1755472031.432787, "relative_start": 3.2629199028015137, "end": 1755472031.432787, "relative_end": 1755472031.432787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": 1755472031.941389, "relative_start": 3.771522045135498, "end": 1755472031.941389, "relative_end": 1755472031.941389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": 1755472032.357841, "relative_start": 4.187973976135254, "end": 1755472032.357841, "relative_end": 1755472032.357841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": 1755472032.67725, "relative_start": 4.507382869720459, "end": 1755472032.67725, "relative_end": 1755472032.67725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.084698, "relative_start": 4.914830923080444, "end": **********.084698, "relative_end": **********.084698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.425122, "relative_start": 5.2552549839019775, "end": **********.425122, "relative_end": **********.425122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.642017, "relative_start": 5.472149848937988, "end": **********.642017, "relative_end": **********.642017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.843063, "relative_start": 5.673196077346802, "end": **********.843063, "relative_end": **********.843063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.046339, "relative_start": 5.876471996307373, "end": **********.046339, "relative_end": **********.046339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.231653, "relative_start": 6.061785936355591, "end": **********.231653, "relative_end": **********.231653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.511966, "relative_start": 6.342098951339722, "end": **********.511966, "relative_end": **********.511966, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.773947, "relative_start": 6.604079961776733, "end": **********.773947, "relative_end": **********.773947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.028368, "relative_start": 6.858500957489014, "end": **********.028368, "relative_end": **********.028368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.30475, "relative_start": 7.134882926940918, "end": **********.30475, "relative_end": **********.30475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.566832, "relative_start": 7.396965026855469, "end": **********.566832, "relative_end": **********.566832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.933153, "relative_start": 7.763285875320435, "end": **********.933153, "relative_end": **********.933153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.276834, "relative_start": 8.106966972351074, "end": **********.276834, "relative_end": **********.276834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.56273, "relative_start": 8.392863035202026, "end": **********.56273, "relative_end": **********.56273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.719549, "relative_start": 8.549681901931763, "end": **********.719549, "relative_end": **********.719549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.904926, "relative_start": 8.735059022903442, "end": **********.904926, "relative_end": **********.904926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.182644, "relative_start": 9.012776851654053, "end": **********.182644, "relative_end": **********.182644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.40845, "relative_start": 9.238582849502563, "end": **********.40845, "relative_end": **********.40845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.pagination", "start": **********.786805, "relative_start": 9.61693787574768, "end": **********.786805, "relative_end": **********.786805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98abfcd8227e681cb9ad72e7d1720a64", "start": **********.793812, "relative_start": 9.623944997787476, "end": **********.793812, "relative_end": **********.793812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.partials.property-map-content", "start": **********.803717, "relative_start": 9.633849859237671, "end": **********.803717, "relative_end": **********.803717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::layouts.full-width", "start": **********.810595, "relative_start": 9.640727996826172, "end": **********.810595, "relative_end": **********.810595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": **********.814357, "relative_start": 9.644490003585815, "end": **********.814357, "relative_end": **********.814357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.header", "start": **********.815141, "relative_start": 9.645273923873901, "end": **********.815141, "relative_end": **********.815141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": **********.823757, "relative_start": 9.653889894485474, "end": **********.823757, "relative_end": **********.823757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": **********.826116, "relative_start": 9.656249046325684, "end": **********.826116, "relative_end": **********.826116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": **********.294268, "relative_start": 12.124400854110718, "end": **********.294268, "relative_end": **********.294268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": **********.295596, "relative_start": 12.125728845596313, "end": **********.295596, "relative_end": **********.295596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.30561, "relative_start": 12.135742902755737, "end": **********.30561, "relative_end": **********.30561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::054b349d8fa17e9f8644145c2fbc1063", "start": **********.307003, "relative_start": 12.137135982513428, "end": **********.307003, "relative_end": **********.307003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6cb431528960518c12f7bad152d7b46d", "start": **********.309325, "relative_start": 12.139457941055298, "end": **********.309325, "relative_end": **********.309325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": **********.311881, "relative_start": 12.142014026641846, "end": **********.311881, "relative_end": **********.311881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f6b8d5c7f3ac3703ebfdef4449133ff4", "start": **********.318635, "relative_start": 12.148767948150635, "end": **********.318635, "relative_end": **********.318635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f6b8d5c7f3ac3703ebfdef4449133ff4", "start": **********.319174, "relative_start": 12.149307012557983, "end": **********.319174, "relative_end": **********.319174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.319541, "relative_start": 12.14967393875122, "end": **********.319541, "relative_end": **********.319541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a7781df99689693edd2c6c189d767089", "start": **********.32049, "relative_start": 12.150622844696045, "end": **********.32049, "relative_end": **********.32049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::054b349d8fa17e9f8644145c2fbc1063", "start": **********.321134, "relative_start": 12.151267051696777, "end": **********.321134, "relative_end": **********.321134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::054b349d8fa17e9f8644145c2fbc1063", "start": **********.32151, "relative_start": 12.15164303779602, "end": **********.32151, "relative_end": **********.32151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6cb431528960518c12f7bad152d7b46d", "start": **********.322458, "relative_start": 12.152590990066528, "end": **********.322458, "relative_end": **********.322458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": **********.32379, "relative_start": 12.153923034667969, "end": **********.32379, "relative_end": **********.32379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.modal-authentication", "start": **********.326387, "relative_start": 12.156519889831543, "end": **********.326387, "relative_end": **********.326387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.33654, "relative_start": 12.166672945022583, "end": **********.33654, "relative_end": **********.33654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form-content-only", "start": **********.339802, "relative_start": 12.16993498802185, "end": **********.339802, "relative_end": **********.339802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.340548, "relative_start": 12.17068099975586, "end": **********.340548, "relative_end": **********.340548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.34148, "relative_start": 12.171612977981567, "end": **********.34148, "relative_end": **********.34148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.342335, "relative_start": 12.172467947006226, "end": **********.342335, "relative_end": **********.342335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.342977, "relative_start": 12.173110008239746, "end": **********.342977, "relative_end": **********.342977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.343551, "relative_start": 12.173683881759644, "end": **********.343551, "relative_end": **********.343551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.email", "start": **********.344307, "relative_start": 12.174439907073975, "end": **********.344307, "relative_end": **********.344307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.345247, "relative_start": 12.175379991531372, "end": **********.345247, "relative_end": **********.345247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.345891, "relative_start": 12.176023960113525, "end": **********.345891, "relative_end": **********.345891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.346606, "relative_start": 12.176738977432251, "end": **********.346606, "relative_end": **********.346606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.346934, "relative_start": 12.177067041397095, "end": **********.346934, "relative_end": **********.346934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.347362, "relative_start": 12.177495002746582, "end": **********.347362, "relative_end": **********.347362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.password", "start": **********.348138, "relative_start": 12.178271055221558, "end": **********.348138, "relative_end": **********.348138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.34891, "relative_start": 12.179043054580688, "end": **********.34891, "relative_end": **********.34891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.349411, "relative_start": 12.17954397201538, "end": **********.349411, "relative_end": **********.349411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.350275, "relative_start": 12.180408000946045, "end": **********.350275, "relative_end": **********.350275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.350586, "relative_start": 12.180718898773193, "end": **********.350586, "relative_end": **********.350586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.350928, "relative_start": 12.181061029434204, "end": **********.350928, "relative_end": **********.350928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.351332, "relative_start": 12.181464910507202, "end": **********.351332, "relative_end": **********.351332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.351739, "relative_start": 12.181871891021729, "end": **********.351739, "relative_end": **********.351739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.352231, "relative_start": 12.182363986968994, "end": **********.352231, "relative_end": **********.352231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.352462, "relative_start": 12.182595014572144, "end": **********.352462, "relative_end": **********.352462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.352779, "relative_start": 12.18291187286377, "end": **********.352779, "relative_end": **********.352779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off-checkbox", "start": **********.353261, "relative_start": 12.183393955230713, "end": **********.353261, "relative_end": **********.353261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off-checkbox", "start": **********.353891, "relative_start": 12.1840238571167, "end": **********.353891, "relative_end": **********.353891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.on-off.checkbox", "start": **********.354542, "relative_start": 12.184674978256226, "end": **********.354542, "relative_end": **********.354542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": **********.355065, "relative_start": 12.185198068618774, "end": **********.355065, "relative_end": **********.355065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.356, "relative_start": 12.186132907867432, "end": **********.356, "relative_end": **********.356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.356497, "relative_start": 12.186630010604858, "end": **********.356497, "relative_end": **********.356497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.356746, "relative_start": 12.18687891960144, "end": **********.356746, "relative_end": **********.356746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.357126, "relative_start": 12.187258958816528, "end": **********.357126, "relative_end": **********.357126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.35764, "relative_start": 12.187772989273071, "end": **********.35764, "relative_end": **********.35764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.358062, "relative_start": 12.188194990158081, "end": **********.358062, "relative_end": **********.358062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.358524, "relative_start": 12.18865704536438, "end": **********.358524, "relative_end": **********.358524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.358749, "relative_start": 12.188881874084473, "end": **********.358749, "relative_end": **********.358749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.359039, "relative_start": 12.18917202949524, "end": **********.359039, "relative_end": **********.359039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.35936, "relative_start": 12.18949294090271, "end": **********.35936, "relative_end": **********.35936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.35972, "relative_start": 12.189852952957153, "end": **********.35972, "relative_end": **********.35972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.360182, "relative_start": 12.190315008163452, "end": **********.360182, "relative_end": **********.360182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.360442, "relative_start": 12.190574884414673, "end": **********.360442, "relative_end": **********.360442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.361174, "relative_start": 12.191307067871094, "end": **********.361174, "relative_end": **********.361174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.361665, "relative_start": 12.191797971725464, "end": **********.361665, "relative_end": **********.361665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.362169, "relative_start": 12.192301988601685, "end": **********.362169, "relative_end": **********.362169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.362858, "relative_start": 12.192991018295288, "end": **********.362858, "relative_end": **********.362858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.363171, "relative_start": 12.193304061889648, "end": **********.363171, "relative_end": **********.363171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.363569, "relative_start": 12.193701982498169, "end": **********.363569, "relative_end": **********.363569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.364021, "relative_start": 12.194154024124146, "end": **********.364021, "relative_end": **********.364021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.364522, "relative_start": 12.194654941558838, "end": **********.364522, "relative_end": **********.364522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.365211, "relative_start": 12.195343971252441, "end": **********.365211, "relative_end": **********.365211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.365532, "relative_start": 12.195664882659912, "end": **********.365532, "relative_end": **********.365532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.365889, "relative_start": 12.196022033691406, "end": **********.365889, "relative_end": **********.365889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::button", "start": **********.366779, "relative_start": 12.196912050247192, "end": **********.366779, "relative_end": **********.366779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.367501, "relative_start": 12.197633981704712, "end": **********.367501, "relative_end": **********.367501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.367965, "relative_start": 12.198097944259644, "end": **********.367965, "relative_end": **********.367965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.368469, "relative_start": 12.198601961135864, "end": **********.368469, "relative_end": **********.368469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.369151, "relative_start": 12.199284076690674, "end": **********.369151, "relative_end": **********.369151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.369474, "relative_start": 12.199606895446777, "end": **********.369474, "relative_end": **********.369474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.369864, "relative_start": 12.199996948242188, "end": **********.369864, "relative_end": **********.369864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.370315, "relative_start": 12.200448036193848, "end": **********.370315, "relative_end": **********.370315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.370821, "relative_start": 12.200953960418701, "end": **********.370821, "relative_end": **********.370821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.371514, "relative_start": 12.20164704322815, "end": **********.371514, "relative_end": **********.371514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.371829, "relative_start": 12.201961994171143, "end": **********.371829, "relative_end": **********.371829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.372169, "relative_start": 12.202301979064941, "end": **********.372169, "relative_end": **********.372169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.372509, "relative_start": 12.20264196395874, "end": **********.372509, "relative_end": **********.372509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.3729, "relative_start": 12.203032970428467, "end": **********.3729, "relative_end": **********.3729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.37338, "relative_start": 12.203512907028198, "end": **********.37338, "relative_end": **********.37338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.373611, "relative_start": 12.203743934631348, "end": **********.373611, "relative_end": **********.373611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.373904, "relative_start": 12.204036951065063, "end": **********.373904, "relative_end": **********.373904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.374218, "relative_start": 12.20435094833374, "end": **********.374218, "relative_end": **********.374218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.374557, "relative_start": 12.204689979553223, "end": **********.374557, "relative_end": **********.374557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.375032, "relative_start": 12.205164909362793, "end": **********.375032, "relative_end": **********.375032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.37527, "relative_start": 12.205402851104736, "end": **********.37527, "relative_end": **********.37527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.375791, "relative_start": 12.205924034118652, "end": **********.375791, "relative_end": **********.375791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.378256, "relative_start": 12.208389043807983, "end": **********.378256, "relative_end": **********.378256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.385604, "relative_start": 12.215736865997314, "end": **********.385604, "relative_end": **********.385604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::forms.auth", "start": **********.387411, "relative_start": 12.217544078826904, "end": **********.387411, "relative_end": **********.387411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2844accb98b3a6a2e1cb0b4f06fc3e6e", "start": **********.388802, "relative_start": 12.218935012817383, "end": **********.388802, "relative_end": **********.388802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.form-open-wrapper", "start": **********.389349, "relative_start": 12.219481945037842, "end": **********.389349, "relative_end": **********.389349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.389784, "relative_start": 12.219917058944702, "end": **********.389784, "relative_end": **********.389784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.390196, "relative_start": 12.22032904624939, "end": **********.390196, "relative_end": **********.390196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.390701, "relative_start": 12.220834016799927, "end": **********.390701, "relative_end": **********.390701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.390961, "relative_start": 12.221093893051147, "end": **********.390961, "relative_end": **********.390961, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.391289, "relative_start": 12.221421957015991, "end": **********.391289, "relative_end": **********.391289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.391737, "relative_start": 12.221869945526123, "end": **********.391737, "relative_end": **********.391737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.392198, "relative_start": 12.222331047058105, "end": **********.392198, "relative_end": **********.392198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.392494, "relative_start": 12.22262692451477, "end": **********.392494, "relative_end": **********.392494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.3931, "relative_start": 12.223232984542847, "end": **********.3931, "relative_end": **********.3931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.393434, "relative_start": 12.223567008972168, "end": **********.393434, "relative_end": **********.393434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.394038, "relative_start": 12.224170923233032, "end": **********.394038, "relative_end": **********.394038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.email", "start": **********.39455, "relative_start": 12.224683046340942, "end": **********.39455, "relative_end": **********.39455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.395125, "relative_start": 12.225257873535156, "end": **********.395125, "relative_end": **********.395125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.395546, "relative_start": 12.22567892074585, "end": **********.395546, "relative_end": **********.395546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.396431, "relative_start": 12.226563930511475, "end": **********.396431, "relative_end": **********.396431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.396751, "relative_start": 12.226883888244629, "end": **********.396751, "relative_end": **********.396751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.397074, "relative_start": 12.227206945419312, "end": **********.397074, "relative_end": **********.397074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.password", "start": **********.397439, "relative_start": 12.227571964263916, "end": **********.397439, "relative_end": **********.397439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.397854, "relative_start": 12.227987051010132, "end": **********.397854, "relative_end": **********.397854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.398129, "relative_start": 12.228261947631836, "end": **********.398129, "relative_end": **********.398129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.398602, "relative_start": 12.228734970092773, "end": **********.398602, "relative_end": **********.398602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.398836, "relative_start": 12.228968858718872, "end": **********.398836, "relative_end": **********.398836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.39914, "relative_start": 12.229272842407227, "end": **********.39914, "relative_end": **********.39914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.399479, "relative_start": 12.229611873626709, "end": **********.399479, "relative_end": **********.399479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.399828, "relative_start": 12.229960918426514, "end": **********.399828, "relative_end": **********.399828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.400295, "relative_start": 12.230427980422974, "end": **********.400295, "relative_end": **********.400295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.400522, "relative_start": 12.230654954910278, "end": **********.400522, "relative_end": **********.400522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.4008, "relative_start": 12.23093295097351, "end": **********.4008, "relative_end": **********.4008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.401127, "relative_start": 12.231260061264038, "end": **********.401127, "relative_end": **********.401127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.401471, "relative_start": 12.231603860855103, "end": **********.401471, "relative_end": **********.401471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.401929, "relative_start": 12.232061862945557, "end": **********.401929, "relative_end": **********.401929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.402157, "relative_start": 12.232290029525757, "end": **********.402157, "relative_end": **********.402157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.402417, "relative_start": 12.232549905776978, "end": **********.402417, "relative_end": **********.402417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::button", "start": **********.40273, "relative_start": 12.232862949371338, "end": **********.40273, "relative_end": **********.40273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.403127, "relative_start": 12.233259916305542, "end": **********.403127, "relative_end": **********.403127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.403451, "relative_start": 12.233583927154541, "end": **********.403451, "relative_end": **********.403451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.403792, "relative_start": 12.233924865722656, "end": **********.403792, "relative_end": **********.403792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.404254, "relative_start": 12.234386920928955, "end": **********.404254, "relative_end": **********.404254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.40448, "relative_start": 12.234612941741943, "end": **********.40448, "relative_end": **********.40448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.404756, "relative_start": 12.234889030456543, "end": **********.404756, "relative_end": **********.404756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.405175, "relative_start": 12.235307931900024, "end": **********.405175, "relative_end": **********.405175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.405564, "relative_start": 12.235697031021118, "end": **********.405564, "relative_end": **********.405564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.406022, "relative_start": 12.236155033111572, "end": **********.406022, "relative_end": **********.406022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.406277, "relative_start": 12.236409902572632, "end": **********.406277, "relative_end": **********.406277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.406571, "relative_start": 12.236703872680664, "end": **********.406571, "relative_end": **********.406571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.40694, "relative_start": 12.237072944641113, "end": **********.40694, "relative_end": **********.40694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.407293, "relative_start": 12.237426042556763, "end": **********.407293, "relative_end": **********.407293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.40778, "relative_start": 12.237912893295288, "end": **********.40778, "relative_end": **********.40778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.408019, "relative_start": 12.238152027130127, "end": **********.408019, "relative_end": **********.408019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.408307, "relative_start": 12.238440036773682, "end": **********.408307, "relative_end": **********.408307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.408654, "relative_start": 12.238786935806274, "end": **********.408654, "relative_end": **********.408654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.409072, "relative_start": 12.23920488357544, "end": **********.409072, "relative_end": **********.409072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.409547, "relative_start": 12.239680051803589, "end": **********.409547, "relative_end": **********.409547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.409818, "relative_start": 12.239950895309448, "end": **********.409818, "relative_end": **********.409818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.410222, "relative_start": 12.240355014801025, "end": **********.410222, "relative_end": **********.410222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.415488, "relative_start": 12.245620965957642, "end": **********.415488, "relative_end": **********.415488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.footer", "start": **********.418588, "relative_start": 12.24872088432312, "end": **********.418588, "relative_end": **********.418588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": **********.421117, "relative_start": 12.25125002861023, "end": **********.421117, "relative_end": **********.421117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::layouts.base", "start": **********.421868, "relative_start": 12.2520010471344, "end": **********.421868, "relative_end": **********.421868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.header", "start": **********.430134, "relative_start": 12.26026701927185, "end": **********.430134, "relative_end": **********.430134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language::partials.hreflang", "start": **********.464834, "relative_start": 12.29496693611145, "end": **********.464834, "relative_end": **********.464834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language::partials.hreflang", "start": **********.470845, "relative_start": 12.300977945327759, "end": **********.470845, "relative_end": **********.470845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.footer", "start": **********.475402, "relative_start": 12.305535078048706, "end": **********.475402, "relative_end": **********.475402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::fronts.toast-notification", "start": **********.477994, "relative_start": 12.30812692642212, "end": **********.477994, "relative_end": **********.477994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.493888, "relative_start": 12.324020862579346, "end": **********.494136, "relative_end": **********.494136, "duration": 0.00024819374084472656, "duration_str": "248μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 71084000, "peak_usage_str": "68MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 197, "nb_templates": 197, "templates": [{"name": "1x theme.xmetr::views.page", "param_count": null, "params": [], "start": **********.991991, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/page.blade.phptheme.xmetr::views.page", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Fpage.blade.php:1", "ajax": false, "filename": "page.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.page"}, {"name": "1x theme.xmetr::views.real-estate.properties", "param_count": null, "params": [], "start": **********.188377, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties.blade.phptheme.xmetr::views.real-estate.properties", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fproperties.blade.php:1", "ajax": false, "filename": "properties.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.properties"}, {"name": "1x theme.xmetr::views.real-estate.partials.listing", "param_count": null, "params": [], "start": **********.207382, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/partials/listing.blade.phptheme.xmetr::views.real-estate.partials.listing", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fpartials%2Flisting.blade.php:1", "ajax": false, "filename": "listing.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.partials.listing"}, {"name": "1x theme.xmetr::views.real-estate.listing-layouts.xmetr", "param_count": null, "params": [], "start": **********.208281, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/listing-layouts/xmetr.blade.phptheme.xmetr::views.real-estate.listing-layouts.xmetr", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Flisting-layouts%2Fxmetr.blade.php:1", "ajax": false, "filename": "xmetr.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.listing-layouts.xmetr"}, {"name": "1x theme.xmetr::views.real-estate.partials.map", "param_count": null, "params": [], "start": **********.413644, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/partials/map.blade.phptheme.xmetr::views.real-estate.partials.map", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fpartials%2Fmap.blade.php:1", "ajax": false, "filename": "map.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.partials.map"}, {"name": "1x theme.xmetr::views.real-estate.properties.index", "param_count": null, "params": [], "start": **********.415984, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/index.blade.phptheme.xmetr::views.real-estate.properties.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fproperties%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.properties.index"}, {"name": "1x theme.xmetr::views.real-estate.properties.grid", "param_count": null, "params": [], "start": **********.41699, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/grid.blade.phptheme.xmetr::views.real-estate.properties.grid", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fproperties%2Fgrid.blade.php:1", "ajax": false, "filename": "grid.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.properties.grid"}, {"name": "24x theme.xmetr::views.real-estate.properties.item-grid", "param_count": null, "params": [], "start": **********.417821, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.phptheme.xmetr::views.real-estate.properties.item-grid", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fproperties%2Fitem-grid.blade.php:1", "ajax": false, "filename": "item-grid.blade.php", "line": "?"}, "render_count": 24, "name_original": "theme.xmetr::views.real-estate.properties.item-grid"}, {"name": "1x theme.xmetr::partials.pagination", "param_count": null, "params": [], "start": **********.786765, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/pagination.blade.phptheme.xmetr::partials.pagination", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Fpagination.blade.php:1", "ajax": false, "filename": "pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.pagination"}, {"name": "1x __components::98abfcd8227e681cb9ad72e7d1720a64", "param_count": null, "params": [], "start": **********.793766, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/98abfcd8227e681cb9ad72e7d1720a64.blade.php__components::98abfcd8227e681cb9ad72e7d1720a64", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F98abfcd8227e681cb9ad72e7d1720a64.blade.php:1", "ajax": false, "filename": "98abfcd8227e681cb9ad72e7d1720a64.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::98abfcd8227e681cb9ad72e7d1720a64"}, {"name": "1x theme.xmetr::views.real-estate.partials.property-map-content", "param_count": null, "params": [], "start": **********.803677, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/partials/property-map-content.blade.phptheme.xmetr::views.real-estate.partials.property-map-content", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fpartials%2Fproperty-map-content.blade.php:1", "ajax": false, "filename": "property-map-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.partials.property-map-content"}, {"name": "1x theme.xmetr::layouts.full-width", "param_count": null, "params": [], "start": **********.810573, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/layouts/full-width.blade.phptheme.xmetr::layouts.full-width", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Flayouts%2Ffull-width.blade.php:1", "ajax": false, "filename": "full-width.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::layouts.full-width"}, {"name": "4x plugins/ads::partials.ad-display", "param_count": null, "params": [], "start": **********.814329, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/ads/resources/views/partials/ad-display.blade.phpplugins/ads::partials.ad-display", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Fads%2Fresources%2Fviews%2Fpartials%2Fad-display.blade.php:1", "ajax": false, "filename": "ad-display.blade.php", "line": "?"}, "render_count": 4, "name_original": "plugins/ads::partials.ad-display"}, {"name": "1x theme.xmetr::partials.header", "param_count": null, "params": [], "start": **********.81512, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/header.blade.phptheme.xmetr::partials.header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.header"}, {"name": "4x plugins/social-login::login-options", "param_count": null, "params": [], "start": **********.82371, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/social-login/resources/views/login-options.blade.phpplugins/social-login::login-options", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Fsocial-login%2Fresources%2Fviews%2Flogin-options.blade.php:1", "ajax": false, "filename": "login-options.blade.php", "line": "?"}, "render_count": 4, "name_original": "plugins/social-login::login-options"}, {"name": "2x __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "param_count": null, "params": [], "start": **********.305588, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php__components::98e88d58787b8dfeb6f0d1dc0a785cfd", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php:1", "ajax": false, "filename": "98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::98e88d58787b8dfeb6f0d1dc0a785cfd"}, {"name": "3x __components::054b349d8fa17e9f8644145c2fbc1063", "param_count": null, "params": [], "start": **********.306982, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/054b349d8fa17e9f8644145c2fbc1063.blade.php__components::054b349d8fa17e9f8644145c2fbc1063", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F054b349d8fa17e9f8644145c2fbc1063.blade.php:1", "ajax": false, "filename": "054b349d8fa17e9f8644145c2fbc1063.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::054b349d8fa17e9f8644145c2fbc1063"}, {"name": "2x __components::6cb431528960518c12f7bad152d7b46d", "param_count": null, "params": [], "start": **********.309304, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/6cb431528960518c12f7bad152d7b46d.blade.php__components::6cb431528960518c12f7bad152d7b46d", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F6cb431528960518c12f7bad152d7b46d.blade.php:1", "ajax": false, "filename": "6cb431528960518c12f7bad152d7b46d.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::6cb431528960518c12f7bad152d7b46d"}, {"name": "2x __components::f6b8d5c7f3ac3703ebfdef4449133ff4", "param_count": null, "params": [], "start": **********.318613, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/f6b8d5c7f3ac3703ebfdef4449133ff4.blade.php__components::f6b8d5c7f3ac3703ebfdef4449133ff4", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ff6b8d5c7f3ac3703ebfdef4449133ff4.blade.php:1", "ajax": false, "filename": "f6b8d5c7f3ac3703ebfdef4449133ff4.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::f6b8d5c7f3ac3703ebfdef4449133ff4"}, {"name": "1x __components::a7781df99689693edd2c6c189d767089", "param_count": null, "params": [], "start": **********.32047, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/a7781df99689693edd2c6c189d767089.blade.php__components::a7781df99689693edd2c6c189d767089", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fa7781df99689693edd2c6c189d767089.blade.php:1", "ajax": false, "filename": "a7781df99689693edd2c6c189d767089.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a7781df99689693edd2c6c189d767089"}, {"name": "1x theme.xmetr::partials.modal-authentication", "param_count": null, "params": [], "start": **********.326365, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/modal-authentication.blade.phptheme.xmetr::partials.modal-authentication", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Fmodal-authentication.blade.php:1", "ajax": false, "filename": "modal-authentication.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.modal-authentication"}, {"name": "4x core/js-validation::bootstrap", "param_count": null, "params": [], "start": **********.336517, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php:1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/js-validation::bootstrap"}, {"name": "1x core/base::forms.form-content-only", "param_count": null, "params": [], "start": **********.339778, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form-content-only.blade.phpcore/base::forms.form-content-only", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform-content-only.blade.php:1", "ajax": false, "filename": "form-content-only.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form-content-only"}, {"name": "17x core/base::forms.fields.html", "param_count": null, "params": [], "start": **********.340527, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/html.blade.phpcore/base::forms.fields.html", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fhtml.blade.php:1", "ajax": false, "filename": "html.blade.php", "line": "?"}, "render_count": 17, "name_original": "core/base::forms.fields.html"}, {"name": "23x ********************************::form.field", "param_count": null, "params": [], "start": **********.341459, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/field.blade.php********************************::form.field", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php:1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 23, "name_original": "********************************::form.field"}, {"name": "23x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": **********.342312, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php:1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 23, "name_original": "core/base::forms.partials.help-block"}, {"name": "23x core/base::forms.partials.errors", "param_count": null, "params": [], "start": **********.342956, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php:1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 23, "name_original": "core/base::forms.partials.errors"}, {"name": "25x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": **********.343517, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php:1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 25, "name_original": "core/base::forms.columns.column-span"}, {"name": "2x core/base::forms.fields.email", "param_count": null, "params": [], "start": **********.344273, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/email.blade.phpcore/base::forms.fields.email", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Femail.blade.php:1", "ajax": false, "filename": "email.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.email"}, {"name": "5x core/base::forms.partials.label", "param_count": null, "params": [], "start": **********.345212, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/label.blade.phpcore/base::forms.partials.label", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Flabel.blade.php:1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 5, "name_original": "core/base::forms.partials.label"}, {"name": "2x core/base::forms.fields.password", "param_count": null, "params": [], "start": **********.348116, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/password.blade.phpcore/base::forms.fields.password", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fpassword.blade.php:1", "ajax": false, "filename": "password.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.password"}, {"name": "1x core/base::forms.fields.on-off-checkbox", "param_count": null, "params": [], "start": **********.353241, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/on-off-checkbox.blade.phpcore/base::forms.fields.on-off-checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fon-off-checkbox.blade.php:1", "ajax": false, "filename": "on-off-checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.on-off-checkbox"}, {"name": "1x core/base::forms.partials.on-off-checkbox", "param_count": null, "params": [], "start": **********.353871, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/on-off-checkbox.blade.phpcore/base::forms.partials.on-off-checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fon-off-checkbox.blade.php:1", "ajax": false, "filename": "on-off-checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.on-off-checkbox"}, {"name": "1x ********************************::form.on-off.checkbox", "param_count": null, "params": [], "start": **********.354522, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/on-off/checkbox.blade.php********************************::form.on-off.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fon-off%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.on-off.checkbox"}, {"name": "1x core/base::components.form.checkbox", "param_count": null, "params": [], "start": **********.355044, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.phpcore/base::components.form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.form.checkbox"}, {"name": "2x laravel-form-builder::button", "param_count": null, "params": [], "start": **********.366745, "type": "php", "hash": "phpD:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src/../resources/views/button.phplaravel-form-builder::button", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fform-builder%2Fresources%2Fviews%2Fbutton.php:1", "ajax": false, "filename": "button.php", "line": "?"}, "render_count": 2, "name_original": "laravel-form-builder::button"}, {"name": "1x plugins/real-estate::forms.auth", "param_count": null, "params": [], "start": **********.387387, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/forms/auth.blade.phpplugins/real-estate::forms.auth", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fforms%2Fauth.blade.php:1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::forms.auth"}, {"name": "1x __components::2844accb98b3a6a2e1cb0b4f06fc3e6e", "param_count": null, "params": [], "start": **********.388779, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/2844accb98b3a6a2e1cb0b4f06fc3e6e.blade.php__components::2844accb98b3a6a2e1cb0b4f06fc3e6e", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F2844accb98b3a6a2e1cb0b4f06fc3e6e.blade.php:1", "ajax": false, "filename": "2844accb98b3a6a2e1cb0b4f06fc3e6e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2844accb98b3a6a2e1cb0b4f06fc3e6e"}, {"name": "1x core/base::forms.columns.form-open-wrapper", "param_count": null, "params": [], "start": **********.389329, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/columns/form-open-wrapper.blade.phpcore/base::forms.columns.form-open-wrapper", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fform-open-wrapper.blade.php:1", "ajax": false, "filename": "form-open-wrapper.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.columns.form-open-wrapper"}, {"name": "1x core/base::forms.fields.text", "param_count": null, "params": [], "start": **********.391717, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/text.blade.phpcore/base::forms.fields.text", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftext.blade.php:1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.text"}, {"name": "1x theme.xmetr::partials.footer", "param_count": null, "params": [], "start": **********.418545, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/footer.blade.phptheme.xmetr::partials.footer", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Ffooter.blade.php:1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.footer"}, {"name": "1x theme.xmetr::layouts.base", "param_count": null, "params": [], "start": **********.42183, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/layouts/base.blade.phptheme.xmetr::layouts.base", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Flayouts%2Fbase.blade.php:1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::layouts.base"}, {"name": "1x packages/theme::partials.header", "param_count": null, "params": [], "start": **********.4301, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/theme/resources/views/partials/header.blade.phppackages/theme::partials.header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.header"}, {"name": "2x plugins/language::partials.hreflang", "param_count": null, "params": [], "start": **********.46481, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/language/resources/views/partials/hreflang.blade.phpplugins/language::partials.hreflang", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fresources%2Fviews%2Fpartials%2Fhreflang.blade.php:1", "ajax": false, "filename": "hreflang.blade.php", "line": "?"}, "render_count": 2, "name_original": "plugins/language::partials.hreflang"}, {"name": "1x packages/theme::partials.footer", "param_count": null, "params": [], "start": **********.475362, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/theme/resources/views/partials/footer.blade.phppackages/theme::partials.footer", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Ffooter.blade.php:1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.footer"}, {"name": "1x packages/theme::fronts.toast-notification", "param_count": null, "params": [], "start": **********.477956, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/theme/resources/views/fronts/toast-notification.blade.phppackages/theme::fronts.toast-notification", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Ffronts%2Ftoast-notification.blade.php:1", "ajax": false, "filename": "toast-notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::fronts.toast-notification"}]}, "queries": {"count": 281, "nb_statements": 281, "nb_visible_statements": 281, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.2003100000000002, "accumulated_duration_str": "200ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 181 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `slugs` where (`key` = 'rent-properties' and `prefix` = '') or exists (select * from `slugs_translations` where `slugs`.`id` = `slugs_translations`.`slugs_id` and (`key` = 'rent-properties' and `prefix` = '')) limit 1", "type": "query", "params": [], "bindings": ["rent-properties", "", "rent-properties", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/SlugHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\SlugHelper.php", "line": 182}, {"index": 19, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.692369, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 0.424}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/packages/page/src/Services/PageService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Services\\PageService.php", "line": 30}], "start": **********.7019281, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0.424, "width_percent": 0.334}, {"sql": "select * from `pages` where (`id` = 14 and `status` = 'published') limit 1", "type": "query", "params": [], "bindings": [14, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/page/src/Services/PageService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Services\\PageService.php", "line": 38}, {"index": 18, "namespace": null, "name": "platform/packages/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Providers\\HookServiceProvider.php", "line": 142}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.708613, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0.759, "width_percent": 0.479}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (14) and `slugs`.`reference_type` = 'Xmetr\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Xmetr\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/page/src/Services/PageService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Services\\PageService.php", "line": 38}, {"index": 24, "namespace": null, "name": "platform/packages/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Providers\\HookServiceProvider.php", "line": 142}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.713897, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 1.238, "width_percent": 0.434}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (14) and `meta_boxes`.`reference_type` = 'Xmetr\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Xmetr\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/packages/seo-helper/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\Providers\\HookServiceProvider.php", "line": 87}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 30, "namespace": null, "name": "platform/packages/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Providers\\HookServiceProvider.php", "line": 142}, {"index": 34, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.756762, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 1.672, "width_percent": 0.295}, {"sql": "select count(*) as aggregate from `re_properties` where (`re_properties`.`moderation_status` = 'approved' and `re_properties`.`status` != 'not_available') and (`expire_date` >= '2025-08-17 23:07:10' or `never_expired` = 1) and `status` != 'rented'", "type": "query", "params": [], "bindings": ["approved", "not_available", "2025-08-17 23:07:10", 1, "rented"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 506}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 262}, {"index": 20, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 665}, {"index": 24, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.102501, "duration": 0.00296, "duration_str": "2.96ms", "memory": 0, "memory_str": null, "filename": "RepositoriesAbstract.php:366", "source": {"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsupport%2Fsrc%2FRepositories%2FEloquent%2FRepositoriesAbstract.php:366", "ajax": false, "filename": "RepositoriesAbstract.php", "line": "366"}, "connection": "xmetr", "explain": null, "start_percent": 1.967, "width_percent": 1.478}, {"sql": "select * from `re_properties` where (`re_properties`.`moderation_status` = 'approved' and `re_properties`.`status` != 'not_available') and (`expire_date` >= '2025-08-17 23:07:10' or `never_expired` = 1) and `status` != 'rented' order by `created_at` desc limit 24 offset 0", "type": "query", "params": [], "bindings": ["approved", "not_available", "2025-08-17 23:07:10", 1, "rented"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 506}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 262}, {"index": 21, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 665}], "start": **********.10835, "duration": 0.0055, "duration_str": "5.5ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 3.445, "width_percent": 2.746}, {"sql": "select `id`, `key`, `prefix`, `reference_id` from `slugs` where `slugs`.`reference_id` in (1206, 1207, 1208, 1209, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227, 1228, 1229, 1230) and `slugs`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property'", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 506}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 262}], "start": **********.123488, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 6.19, "width_percent": 0.359}, {"sql": "select `id`, `name` from `states` where `states`.`id` in (192, 193, 197, 206)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 506}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 262}], "start": **********.13902, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 6.55, "width_percent": 0.28}, {"sql": "select `id`, `name` from `cities` where `cities`.`id` in (8189, 8204, 8205, 8206, 8211, 8212, 8216, 8297, 8316)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 506}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 262}], "start": **********.147608, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 6.829, "width_percent": 0.31}, {"sql": "select `id`, `is_default`, `exchange_rate`, `symbol`, `title`, `is_prefix_symbol` from `re_currencies` where `re_currencies`.`id` in (4, 10, 36)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 506}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 262}], "start": **********.15771, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 7.139, "width_percent": 0.534}, {"sql": "select `re_categories`.`id`, `re_categories`.`name`, `re_property_categories`.`property_id` as `pivot_property_id`, `re_property_categories`.`category_id` as `pivot_category_id` from `re_categories` inner join `re_property_categories` on `re_categories`.`id` = `re_property_categories`.`category_id` where `re_property_categories`.`property_id` in (1206, 1207, 1208, 1209, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227, 1228, 1229, 1230) and `status` = 'published' order by `created_at` desc, `is_default` desc, `order` desc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 22, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 506}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 262}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 665}], "start": **********.174774, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 7.673, "width_percent": 0.914}, {"sql": "select `id`, `name` from `pages` where `status` = 'published' and `id` = '14' limit 1", "type": "query", "params": [], "bindings": ["published", "14"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 168}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 183}, {"index": 20, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties.blade.php", "line": 6}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.190213, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 8.587, "width_percent": 1.608}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (14) and `slugs`.`reference_type` = 'Xmetr\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Xmetr\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 168}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 183}, {"index": 26, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties.blade.php", "line": 6}], "start": **********.195604, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 10.194, "width_percent": 0.359}, {"sql": "select * from `countries` where exists (select * from `re_properties` where `countries`.`id` = `re_properties`.`country_id`) order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 141}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.211201, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 10.554, "width_percent": 0.824}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (22, 24, 27, 29, 30, 31, 32, 33, 34, 35, 37, 44, 49, 52, 110, 115, 149, 199) and `slugs`.`reference_type` = 'Xmetr\\\\Location\\\\Models\\\\Country'", "type": "query", "params": [], "bindings": ["Xmetr\\Location\\Models\\Country"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 141}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2191339, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 11.377, "width_percent": 1.692}, {"sql": "select * from `re_categories` where (`status` = 'published') order by `created_at` asc, `is_default` asc, `order` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/CategoryRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\CategoryRepository.php", "line": 21}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/helpers/helpers.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\helpers.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.344946, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 13.07, "width_percent": 0.265}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (4, 7, 8) and `slugs`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Category'", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Category"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/CategoryRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\CategoryRepository.php", "line": 21}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/helpers/helpers.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\helpers.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.347831, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 13.334, "width_percent": 0.225}, {"sql": "select * from `re_features` where exists (select * from `re_properties` inner join `re_property_features` on `re_properties`.`id` = `re_property_features`.`property_id` where `re_features`.`id` = `re_property_features`.`feature_id`) and `status` = 'published'", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": "view", "name": "theme.xmetr::views.real-estate.listing-layouts.xmetr", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/listing-layouts/xmetr.blade.php", "line": 360}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.359563, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 13.559, "width_percent": 0.344}, {"sql": "select * from `re_spoken_languages` where `status` = 'published' order by `name` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": "view", "name": "theme.xmetr::views.real-estate.listing-layouts.xmetr", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/listing-layouts/xmetr.blade.php", "line": 415}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.395925, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 13.903, "width_percent": 0.32}, {"sql": "select * from `re_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/helpers/currencies.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\currencies.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.4475129, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 14.223, "width_percent": 0.384}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1230 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1230], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.701266, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 14.607, "width_percent": 0.315}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 152}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.866803, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 14.922, "width_percent": 0.265}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1230", "type": "query", "params": [], "bindings": [1230], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 158}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.874007, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 15.186, "width_percent": 1.647}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 8 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.880654, "duration": 0.0096, "duration_str": "9.6ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 16.834, "width_percent": 4.793}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.895052, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 21.626, "width_percent": 0.464}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 16 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.902524, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 22.091, "width_percent": 0.265}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 154 limit 1", "type": "query", "params": [], "bindings": [154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 199}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.911223, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 22.355, "width_percent": 0.364}, {"sql": "select * from `media_files` where `media_files`.`id` = 9027 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9027], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 206}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.919408, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 22.72, "width_percent": 0.344}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 154 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 252}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.928193, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 23.064, "width_percent": 0.255}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1229 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1229], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472031.206097, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 23.319, "width_percent": 0.529}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 152}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755472031.372999, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 23.848, "width_percent": 0.27}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1229", "type": "query", "params": [], "bindings": [1229], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 158}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1755472031.379786, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 24.118, "width_percent": 0.295}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 8 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472031.38377, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 24.412, "width_percent": 0.235}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 13 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 13], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472031.3891752, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 24.647, "width_percent": 0.24}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472031.3943272, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 24.886, "width_percent": 0.245}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 16 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472031.401345, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 25.131, "width_percent": 0.315}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 154 limit 1", "type": "query", "params": [], "bindings": [154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 199}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755472031.408486, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 25.446, "width_percent": 0.394}, {"sql": "select * from `media_files` where `media_files`.`id` = 9027 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9027], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 206}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755472031.413753, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 25.84, "width_percent": 1.827}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 154 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 252}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472031.423463, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 27.667, "width_percent": 1.463}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1228 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1228], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472031.6873739, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 29.13, "width_percent": 0.28}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 152}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755472031.857982, "duration": 0.01095, "duration_str": "10.95ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 29.409, "width_percent": 5.467}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1228", "type": "query", "params": [], "bindings": [1228], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 158}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1755472031.8764348, "duration": 0.02361, "duration_str": "23.61ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 34.876, "width_percent": 11.787}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 2 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472031.90377, "duration": 0.00474, "duration_str": "4.74ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 46.663, "width_percent": 2.366}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 8 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472031.9133928, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 49.029, "width_percent": 0.305}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 13 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 13], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472031.918741, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 49.334, "width_percent": 0.205}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472031.9220111, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 49.538, "width_percent": 0.225}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 16 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472031.9263968, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 49.763, "width_percent": 0.215}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 154 limit 1", "type": "query", "params": [], "bindings": [154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 199}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755472031.930664, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 49.978, "width_percent": 0.235}, {"sql": "select * from `media_files` where `media_files`.`id` = 9027 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9027], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 206}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755472031.933365, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 50.212, "width_percent": 0.225}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 154 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 252}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472031.9375238, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 50.437, "width_percent": 0.21}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1227 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1227], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472032.165439, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 50.646, "width_percent": 0.285}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 152}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755472032.31487, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 50.931, "width_percent": 0.245}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1227", "type": "query", "params": [], "bindings": [1227], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 158}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1755472032.320193, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 51.176, "width_percent": 0.315}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 2 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472032.324233, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 51.49, "width_percent": 0.359}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 8 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472032.3296459, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 51.85, "width_percent": 0.295}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472032.333308, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 52.144, "width_percent": 0.2}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 16 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472032.340705, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 52.344, "width_percent": 0.449}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 154 limit 1", "type": "query", "params": [], "bindings": [154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 199}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755472032.346234, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 52.793, "width_percent": 0.235}, {"sql": "select * from `media_files` where `media_files`.`id` = 9027 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9027], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 206}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755472032.348572, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 53.028, "width_percent": 0.25}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 154 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 252}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472032.353854, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 53.277, "width_percent": 0.205}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1226 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1226], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472032.546111, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 53.482, "width_percent": 0.225}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 152}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755472032.641845, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 53.707, "width_percent": 0.225}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1226", "type": "query", "params": [], "bindings": [1226], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 158}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1755472032.645728, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 53.931, "width_percent": 0.22}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 13 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 13], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472032.6480372, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 54.151, "width_percent": 0.225}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472032.6541212, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 54.376, "width_percent": 0.334}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 16 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472032.660162, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 54.71, "width_percent": 0.2}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 98 limit 1", "type": "query", "params": [], "bindings": [98], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 199}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755472032.6636631, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 54.91, "width_percent": 0.225}, {"sql": "select * from `media_files` where `media_files`.`id` = 4600 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4600], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 206}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755472032.6658, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 55.135, "width_percent": 0.155}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 98 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 98], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 252}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472032.6701438, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 55.289, "width_percent": 0.305}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1225 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1225], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755472032.9073422, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 55.594, "width_percent": 0.664}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 152}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.0425608, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 56.258, "width_percent": 0.3}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1225", "type": "query", "params": [], "bindings": [1225], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 158}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.048232, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 56.557, "width_percent": 0.255}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 8 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.051264, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 56.812, "width_percent": 0.26}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 13 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 13], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.0565548, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 57.072, "width_percent": 0.215}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.061251, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 57.286, "width_percent": 0.25}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 98 limit 1", "type": "query", "params": [], "bindings": [98], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 199}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.069775, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 57.536, "width_percent": 0.23}, {"sql": "select * from `media_files` where `media_files`.`id` = 4600 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4600], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 206}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.0733368, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 57.765, "width_percent": 0.295}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 98 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 98], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 252}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.080167, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 58.06, "width_percent": 0.235}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1224 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1224], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.298564, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 58.295, "width_percent": 0.24}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 152}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3916142, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 58.534, "width_percent": 0.23}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1224", "type": "query", "params": [], "bindings": [1224], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 158}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.395191, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 58.764, "width_percent": 0.19}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 8 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.3977828, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 58.954, "width_percent": 0.215}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.402905, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 59.168, "width_percent": 0.23}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 132 limit 1", "type": "query", "params": [], "bindings": [132], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 199}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.409724, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 59.398, "width_percent": 0.379}, {"sql": "select * from `media_files` where `media_files`.`id` = 7004 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7004], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 206}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.41395, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 59.777, "width_percent": 0.275}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 132 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 132], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 252}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.418725, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 60.052, "width_percent": 0.23}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1223 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1223], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.600323, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 60.282, "width_percent": 0.185}, {"sql": "select * from `countries` where `countries`.`id` = 34 limit 1", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 152}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6059048, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 60.466, "width_percent": 0.21}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1223", "type": "query", "params": [], "bindings": [1223], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 158}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.6104572, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 60.676, "width_percent": 0.225}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 2 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.612665, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 60.901, "width_percent": 0.155}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 7 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.6156201, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 61.055, "width_percent": 0.15}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 13 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 13], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.618586, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 61.205, "width_percent": 0.19}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.621855, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 61.395, "width_percent": 0.2}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 16 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.626225, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 61.595, "width_percent": 0.17}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 74 limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 199}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.630048, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 61.764, "width_percent": 0.175}, {"sql": "select * from `media_files` where `media_files`.`id` = 3233 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [3233], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 206}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.632035, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 61.939, "width_percent": 0.175}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 74 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 74], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 252}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.635745, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 62.114, "width_percent": 0.16}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1222 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1222], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.806297, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 62.273, "width_percent": 0.27}, {"sql": "select * from `countries` where `countries`.`id` = 52 limit 1", "type": "query", "params": [], "bindings": [52], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 152}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.813258, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 62.543, "width_percent": 0.18}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.816336, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.723, "width_percent": 0.125}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.817647, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.848, "width_percent": 0.105}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.81975, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.952, "width_percent": 0.1}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.822103, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.052, "width_percent": 0.16}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8243248, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.212, "width_percent": 0.13}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8264458, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.342, "width_percent": 0.13}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.829597, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.472, "width_percent": 0.13}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.831723, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.601, "width_percent": 0.13}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.834702, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.731, "width_percent": 0.16}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.835957, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.891, "width_percent": 0.135}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.839669, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.026, "width_percent": 0.215}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.99683, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.24, "width_percent": 0.165}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.001124, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.405, "width_percent": 0.215}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.005449, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.62, "width_percent": 0.225}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.008413, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.844, "width_percent": 0.434}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.013536, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.279, "width_percent": 0.215}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.016364, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.493, "width_percent": 0.145}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.018855, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.638, "width_percent": 0.145}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.021285, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.783, "width_percent": 0.17}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0286, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.953, "width_percent": 0.334}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0317981, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.287, "width_percent": 0.16}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.034679, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.447, "width_percent": 0.16}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.035991, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.607, "width_percent": 0.125}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.04084, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.732, "width_percent": 0.27}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.190854, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.001, "width_percent": 0.185}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.194629, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.186, "width_percent": 0.16}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.197087, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.346, "width_percent": 0.155}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.198529, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.5, "width_percent": 0.14}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2008278, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.64, "width_percent": 0.11}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2029738, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.75, "width_percent": 0.12}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.205825, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.87, "width_percent": 0.424}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2106419, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.294, "width_percent": 0.369}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.217393, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.664, "width_percent": 0.2}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.221495, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.863, "width_percent": 0.27}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.224654, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.133, "width_percent": 0.175}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2259932, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.308, "width_percent": 0.155}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2286081, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.462, "width_percent": 0.17}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.369446, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.632, "width_percent": 0.155}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.475368, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.787, "width_percent": 0.285}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4797451, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.071, "width_percent": 0.24}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.482474, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.311, "width_percent": 0.165}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.484986, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.476, "width_percent": 0.145}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.487411, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.621, "width_percent": 0.165}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.489925, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.785, "width_percent": 0.185}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.493737, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.97, "width_percent": 0.155}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.496243, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.125, "width_percent": 0.165}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.500746, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.29, "width_percent": 0.17}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.502289, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.459, "width_percent": 0.15}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.50644, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.609, "width_percent": 0.26}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.648947, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.869, "width_percent": 0.155}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.749023, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.023, "width_percent": 0.18}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.751779, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.203, "width_percent": 0.155}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.753163, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.358, "width_percent": 0.14}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.756351, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.498, "width_percent": 0.23}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7595692, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.727, "width_percent": 0.135}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.761725, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.862, "width_percent": 0.115}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.765019, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.977, "width_percent": 0.16}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.770126, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.137, "width_percent": 0.165}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.90468, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.301, "width_percent": 0.25}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0061831, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.551, "width_percent": 0.225}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.010685, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.776, "width_percent": 0.26}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.012326, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.035, "width_percent": 0.15}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0147579, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.185, "width_percent": 0.13}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.018015, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.315, "width_percent": 0.13}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0212061, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.445, "width_percent": 0.165}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.022625, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.609, "width_percent": 0.13}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.025476, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.739, "width_percent": 0.14}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.172163, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.879, "width_percent": 0.21}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.280939, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.089, "width_percent": 0.235}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.285316, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.323, "width_percent": 0.21}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.287561, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.533, "width_percent": 0.175}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.29178, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.708, "width_percent": 0.31}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2969499, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.017, "width_percent": 0.364}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.298814, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.382, "width_percent": 0.185}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3018332, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.566, "width_percent": 0.16}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.451399, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.726, "width_percent": 0.155}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.541022, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.881, "width_percent": 0.25}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.54414, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.13, "width_percent": 0.315}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.546263, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.445, "width_percent": 0.185}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.548777, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.63, "width_percent": 0.17}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5513759, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.799, "width_percent": 0.12}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.555475, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.919, "width_percent": 0.2}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5595279, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.119, "width_percent": 0.185}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5609531, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.304, "width_percent": 0.15}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.563915, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.453, "width_percent": 0.15}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.763697, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.603, "width_percent": 0.29}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8876889, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.893, "width_percent": 0.215}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.892094, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.107, "width_percent": 0.265}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.894345, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.372, "width_percent": 0.185}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.898193, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.557, "width_percent": 0.175}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.900635, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.731, "width_percent": 0.145}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.902941, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.876, "width_percent": 0.14}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.905406, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.016, "width_percent": 0.195}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9094539, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.211, "width_percent": 0.2}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9143631, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.41, "width_percent": 0.25}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.918423, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.66, "width_percent": 0.185}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.924958, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.845, "width_percent": 0.23}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.927143, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.074, "width_percent": 0.16}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.930212, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.234, "width_percent": 0.15}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.126285, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.384, "width_percent": 0.19}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.253129, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.574, "width_percent": 0.27}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.257792, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.843, "width_percent": 0.389}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2600908, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.233, "width_percent": 0.29}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.263403, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.522, "width_percent": 0.16}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.267646, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.682, "width_percent": 0.175}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2690592, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.857, "width_percent": 0.14}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.272047, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.996, "width_percent": 0.155}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.42052, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.151, "width_percent": 0.19}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.527804, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.341, "width_percent": 0.21}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.530662, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.55, "width_percent": 0.18}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.532156, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.73, "width_percent": 0.145}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5345562, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.875, "width_percent": 0.155}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5369442, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.03, "width_percent": 0.14}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.539934, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.17, "width_percent": 0.205}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5463111, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.374, "width_percent": 0.23}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.550412, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.604, "width_percent": 0.155}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.554397, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.759, "width_percent": 0.175}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.555898, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.933, "width_percent": 0.19}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.559738, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.123, "width_percent": 0.175}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.689794, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.298, "width_percent": 0.225}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.696182, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.522, "width_percent": 0.155}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6988811, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.677, "width_percent": 0.155}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.700263, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.832, "width_percent": 0.145}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.702573, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.977, "width_percent": 0.14}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.705889, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.117, "width_percent": 0.14}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.71083, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.256, "width_percent": 0.349}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.716527, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.606, "width_percent": 0.155}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.862837, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.761, "width_percent": 0.16}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.867357, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.92, "width_percent": 0.145}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.870042, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.065, "width_percent": 0.17}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.872252, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.235, "width_percent": 0.21}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.876504, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.444, "width_percent": 0.18}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.880579, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.624, "width_percent": 0.185}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.883317, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.809, "width_percent": 0.155}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.885796, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.964, "width_percent": 0.145}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.888715, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.108, "width_percent": 0.165}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.893687, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.273, "width_percent": 0.2}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.896615, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.473, "width_percent": 0.18}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.898564, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.653, "width_percent": 0.165}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.90175, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.817, "width_percent": 0.18}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.053488, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.997, "width_percent": 0.15}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1492732, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.147, "width_percent": 0.235}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.152034, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.381, "width_percent": 0.185}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.153573, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.566, "width_percent": 0.15}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.155863, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.716, "width_percent": 0.155}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.159102, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.871, "width_percent": 0.17}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.161442, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.04, "width_percent": 0.14}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1636982, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.18, "width_percent": 0.145}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.165986, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.325, "width_percent": 0.14}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.169279, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.465, "width_percent": 0.14}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.173376, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.605, "width_percent": 0.359}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1756248, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.964, "width_percent": 0.19}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1792488, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.154, "width_percent": 0.295}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.340626, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.448, "width_percent": 0.305}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3493528, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.753, "width_percent": 0.394}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.355164, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.147, "width_percent": 0.255}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.358201, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.402, "width_percent": 0.255}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.362819, "duration": 0.0044599999999999996, "duration_str": "4.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.656, "width_percent": 2.227}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.371618, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.883, "width_percent": 0.24}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.376383, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.123, "width_percent": 0.235}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.380482, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.357, "width_percent": 0.175}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.386439, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.532, "width_percent": 0.26}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3932798, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.792, "width_percent": 0.3}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3960452, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.091, "width_percent": 0.23}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.402218, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.321, "width_percent": 0.235}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.634638, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.555, "width_percent": 0.28}, {"sql": "select * from `countries` where `countries`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.751775, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.835, "width_percent": 0.205}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.754529, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.04, "width_percent": 0.165}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7560081, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.204, "width_percent": 0.155}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.75982, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.359, "width_percent": 0.265}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.76419, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.624, "width_percent": 0.26}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.770271, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.883, "width_percent": 0.245}, {"sql": "select * from `media_files` where `media_files`.`id` = ? and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.772574, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.128, "width_percent": 0.185}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.777789, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.313, "width_percent": 0.245}, {"sql": "select * from `ads`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.81298, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.557, "width_percent": 0.2}, {"sql": "select * from `slugs` where `reference_id` = ? and `reference_type` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.458473, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.757, "width_percent": 0.27}, {"sql": "select * from `slugs_translations` where `slugs_translations`.`slugs_id` in (131)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.459766, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.027, "width_percent": 0.18}, {"sql": "select * from `slugs` where `reference_id` = ? and `reference_type` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4630802, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.206, "width_percent": 0.245}, {"sql": "select * from `slugs` where `reference_id` = ? and `reference_type` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.466553, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.451, "width_percent": 0.205}, {"sql": "select * from `slugs_translations` where `slugs_translations`.`slugs_id` in (131)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.467427, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.656, "width_percent": 0.155}, {"sql": "select * from `slugs` where `reference_id` = ? and `reference_type` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.469518, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.81, "width_percent": 0.19}]}, "models": {"data": {"Xmetr\\Base\\Models\\MetaBox": {"value": 255, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php:1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Feature": {"value": 132, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FFeature.php:1", "ajax": false, "filename": "Feature.php", "line": "?"}}, "Xmetr\\Slug\\Models\\Slug": {"value": 75, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php:1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Currency": {"value": 46, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Xmetr\\Location\\Models\\Country": {"value": 42, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCountry.php:1", "ajax": false, "filename": "Country.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Category": {"value": 27, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCategory.php:1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Property": {"value": 24, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProperty.php:1", "ajax": false, "filename": "Property.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Account": {"value": 24, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FAccount.php:1", "ajax": false, "filename": "Account.php", "line": "?"}}, "Xmetr\\Media\\Models\\MediaFile": {"value": 22, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFile.php:1", "ajax": false, "filename": "MediaFile.php", "line": "?"}}, "Xmetr\\Location\\Models\\City": {"value": 9, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCity.php:1", "ajax": false, "filename": "City.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\SpokenLanguage": {"value": 9, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FSpokenLanguage.php:1", "ajax": false, "filename": "SpokenLanguage.php", "line": "?"}}, "Xmetr\\LanguageAdvanced\\Models\\TranslationResolver": {"value": 8, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FModels%2FTranslationResolver.php:1", "ajax": false, "filename": "TranslationResolver.php", "line": "?"}}, "Xmetr\\Location\\Models\\State": {"value": 4, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FState.php:1", "ajax": false, "filename": "State.php", "line": "?"}}, "Xmetr\\Page\\Models\\Page": {"value": 2, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fpage%2Fsrc%2FModels%2FPage.php:1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 680, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/en/rent-properties", "action_name": "public.single", "controller_action": "Xmetr\\Theme\\Http\\Controllers\\PublicController@getView", "uri": "GET en/{slug?}", "controller": "Xmetr\\Theme\\Http\\Controllers\\PublicController@getView<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FHttp%2FControllers%2FPublicController.php:41\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/en", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FHttp%2FControllers%2FPublicController.php:41\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/packages/theme/src/Http/Controllers/PublicController.php:41-93</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect", "duration": "12.36s", "peak_memory": "70MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-197609651 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-197609651\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-25220383 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-25220383\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1839420944 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">https://xmetr.gc/en/rent-properties</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1130 characters\">_hjSession_6417422=eyJpZCI6Ijk4M2NmMjcwLTk2OTgtNDNlYi05OGI1LTM1MmRlYmQyOTNkNCIsImMiOjE3NTU0NzExNzc1NDgsInMiOjAsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxfQ==; _ga=GA1.1.1927348753.1755471178; _hjSessionUser_6417422=eyJpZCI6IjFmM2QzZWNhLTA1MzktNTFlMS1hNmJjLTBjY2QyNDE5NDI5MCIsImNyZWF0ZWQiOjE3NTU0NzExNzc1NDYsImV4aXN0aW5nIjp0cnVlfQ==; cookie_for_consent=1; _ga_KQ6X76DET4=GS2.1.s1755471178$o1$g1$t1755472023$j17$l0$h0; XSRF-TOKEN=eyJpdiI6IjhUbEN2a2s4aVgva1pwYkpzNHNVQUE9PSIsInZhbHVlIjoiU1J0cmpQQlBsQ1ptWjhCVk5naTQ1bzRWMHBObXBLTk44NjhJdlU3a252Nkk3Uzllb2FqOUJEbjArR2JjQ0VObGhxZnQrNGpLcVpvVXM0WmU1dVRSVWVJNHhCNmdBbGZwd2xWYUtkYW5kYUo5dUZxak9kNFRUV1QySkNVT1RhUVIiLCJtYWMiOiI0ZGM1YjJkYTM5OGRmMjM2NzE5NDRiMWIwMzM2MzY5MzAwODRjNGNkMjhkNzQ5NTU0NGVlYmFjODRkOWEyMzhjIiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6Ik9DUDRQOXg3YVNYZm9QUE1vQXN0OEE9PSIsInZhbHVlIjoiNUVqeWpKakNQZWkyV2lGVlJCbDYvMTNOdzE5cnlVS01FcnFWeFBKa1hoYzFnc3R3SGxZRGVFWnhDYytuWjlUZEs2RVJwNTFRTnhjbEs5U3hzTTdUYTR4ckhZN05xTHFDeXdrUUxPdjJKT2xOdVJzcjhZN1B6NVIxTWhXeitnZXUiLCJtYWMiOiIwNTI1MzJiNjAyZGFmMThjOWQwMTViY2I5MGExYjQ3ZGRkMWI3YWZjMzlmYWY4ZTM1ZWNjYjE0NjVhZmZmMWM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1839420944\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1406325365 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SpavgfhqPsU5JT0rwVwub2px7qWmwj0ouWvQBFmZ</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uvBdtHpHElv7tkBreJNh86xe9kTYweWfvpeiseVe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1406325365\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-271452707 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 17 Aug 2025 23:07:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>xmetr-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">7.4.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>developer-name</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Ishtiaq Ahmed</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>project-name</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">Xmetr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-271452707\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2088883881 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SpavgfhqPsU5JT0rwVwub2px7qWmwj0ouWvQBFmZ</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">https://xmetr.gc/en/rent-properties</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>viewed_property</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>96</span> => <span class=sf-dump-num>1755454079</span>\n    <span class=sf-dump-key>747</span> => <span class=sf-dump-num>1755454176</span>\n    <span class=sf-dump-key>90</span> => <span class=sf-dump-num>1755455154</span>\n    <span class=sf-dump-key>93</span> => <span class=sf-dump-num>1755455940</span>\n    <span class=sf-dump-key>762</span> => <span class=sf-dump-num>1755456393</span>\n    <span class=sf-dump-key>1228</span> => <span class=sf-dump-num>1755456631</span>\n    <span class=sf-dump-key>1227</span> => <span class=sf-dump-num>1755456720</span>\n    <span class=sf-dump-key>1230</span> => <span class=sf-dump-num>1755459614</span>\n    <span class=sf-dump-key>1229</span> => <span class=sf-dump-num>1755469502</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property_daily</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>96</span> => <span class=sf-dump-num>1755454079</span>\n    <span class=sf-dump-key>747</span> => <span class=sf-dump-num>1755454176</span>\n    <span class=sf-dump-key>90</span> => <span class=sf-dump-num>1755455154</span>\n    <span class=sf-dump-key>93</span> => <span class=sf-dump-num>1755455940</span>\n    <span class=sf-dump-key>762</span> => <span class=sf-dump-num>1755456393</span>\n    <span class=sf-dump-key>1228</span> => <span class=sf-dump-num>1755456631</span>\n    <span class=sf-dump-key>1227</span> => <span class=sf-dump-num>1755456720</span>\n    <span class=sf-dump-key>1230</span> => <span class=sf-dump-num>1755459614</span>\n    <span class=sf-dump-key>1229</span> => <span class=sf-dump-num>1755469502</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>contact_click_property_1230_telegram</span>\" => <span class=sf-dump-num>1755459691</span>\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n  \"<span class=sf-dump-key>previous_language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ru</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2088883881\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/en/rent-properties", "action_name": "public.single", "controller_action": "Xmetr\\Theme\\Http\\Controllers\\PublicController@getView"}, "badge": null}}