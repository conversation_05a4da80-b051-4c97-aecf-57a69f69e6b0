{"__meta": {"id": "01K2X4RQ7BAQHMNNZ2YJNGZKYY", "datetime": "2025-08-17 23:10:23", "utime": **********.468886, "method": "GET", "uri": "/en/properties/long-term-rental-of-a-2-room-apartment-48m2-krakow-poland", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[23:10:18] LOG.error: Value \"\" is not part of the enum Xmetr\\RealEstate\\Enums\\RentalPeriodEnum", "message_html": null, "is_string": false, "label": "error", "time": **********.922974, "xdebug_link": null, "collector": "log"}, {"message": "[23:10:20] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in D:\\laragon\\www\\xmetr\\storage\\framework\\views\\03b54f071e37eb41b5e8510b59b8e693.php on line 146", "message_html": null, "is_string": false, "label": "warning", "time": **********.069135, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.051518, "end": **********.46892, "duration": 6.417402029037476, "duration_str": "6.42s", "measures": [{"label": "Booting", "start": **********.051518, "relative_start": 0, "end": **********.96802, "relative_end": **********.96802, "duration": 0.****************, "duration_str": "917ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.968035, "relative_start": 0.****************, "end": **********.468923, "relative_end": 3.0994415283203125e-06, "duration": 5.***************, "duration_str": "5.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.996573, "relative_start": 0.****************, "end": **********.026854, "relative_end": **********.026854, "duration": 0.*****************, "duration_str": "30.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: theme.xmetr::views.real-estate.property", "start": **********.326812, "relative_start": 1.****************, "end": **********.326812, "relative_end": **********.326812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.single-layouts.style-5", "start": **********.587981, "relative_start": 1.****************, "end": **********.587981, "relative_end": **********.587981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.single-layouts.partials.gallery-grid", "start": **********.589023, "relative_start": 1.5375051498413086, "end": **********.589023, "relative_end": **********.589023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.single-layouts.partials.description", "start": **********.900853, "relative_start": 1.849334955215454, "end": **********.900853, "relative_end": **********.900853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.single-layouts.partials.features", "start": **********.909505, "relative_start": 1.8579869270324707, "end": **********.909505, "relative_end": **********.909505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.single-layouts.partials.facilities", "start": **********.915921, "relative_start": 1.8644030094146729, "end": **********.915921, "relative_end": **********.915921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.single-layouts.partials.video-gallery", "start": **********.918828, "relative_start": 1.8673100471496582, "end": **********.918828, "relative_end": **********.918828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.single-layouts.partials.rental-conditions", "start": **********.919461, "relative_start": 1.8679430484771729, "end": **********.919461, "relative_end": **********.919461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.single-layouts.partials.district-ratings", "start": **********.924116, "relative_start": 1.8725979328155518, "end": **********.924116, "relative_end": **********.924116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.single-layouts.partials.map", "start": **********.925002, "relative_start": 1.8734841346740723, "end": **********.925002, "relative_end": **********.925002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.single-layouts.partials.project", "start": **********.929134, "relative_start": 1.8776159286499023, "end": **********.929134, "relative_end": **********.929134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.single-layouts.partials.agent", "start": **********.930174, "relative_start": 1.8786561489105225, "end": **********.930174, "relative_end": **********.930174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.single-layouts.partials.related-properties", "start": **********.956942, "relative_start": 1.9054241180419922, "end": **********.956942, "relative_end": **********.956942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.009039, "relative_start": 1.9575209617614746, "end": **********.009039, "relative_end": **********.009039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.367554, "relative_start": 2.3160359859466553, "end": **********.367554, "relative_end": **********.367554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.654879, "relative_start": 2.603361129760742, "end": **********.654879, "relative_end": **********.654879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dd248739db8ba923ebfe1f426bb71a38", "start": **********.049951, "relative_start": 2.9984331130981445, "end": **********.049951, "relative_end": **********.049951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5def649a5a47936dda7e47db6ffcfa75", "start": **********.065908, "relative_start": 3.014389991760254, "end": **********.065908, "relative_end": **********.065908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::de8568c69a934121dc0e6e0a5eccedeb", "start": **********.067865, "relative_start": 3.0163469314575195, "end": **********.067865, "relative_end": **********.067865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c56464a7585811c54c34ced5fe63e79", "start": **********.070191, "relative_start": 3.0186729431152344, "end": **********.070191, "relative_end": **********.070191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::layouts.full-width", "start": **********.073061, "relative_start": 3.021543025970459, "end": **********.073061, "relative_end": **********.073061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": **********.13656, "relative_start": 3.0850419998168945, "end": **********.13656, "relative_end": **********.13656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.header", "start": **********.138038, "relative_start": 3.086519956588745, "end": **********.138038, "relative_end": **********.138038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": **********.18962, "relative_start": 3.1381020545959473, "end": **********.18962, "relative_end": **********.18962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": **********.194243, "relative_start": 3.1427249908447266, "end": **********.194243, "relative_end": **********.194243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": 1755472222.87669, "relative_start": 5.825171947479248, "end": 1755472222.87669, "relative_end": 1755472222.87669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.breadcrumb", "start": 1755472222.878294, "relative_start": 5.826776027679443, "end": 1755472222.878294, "relative_end": 1755472222.878294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": 1755472222.884444, "relative_start": 5.832926034927368, "end": 1755472222.884444, "relative_end": 1755472222.884444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": 1755472222.905314, "relative_start": 5.853796005249023, "end": 1755472222.905314, "relative_end": 1755472222.905314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::054b349d8fa17e9f8644145c2fbc1063", "start": 1755472222.907357, "relative_start": 5.855839014053345, "end": 1755472222.907357, "relative_end": 1755472222.907357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6cb431528960518c12f7bad152d7b46d", "start": 1755472222.911467, "relative_start": 5.859949111938477, "end": 1755472222.911467, "relative_end": 1755472222.911467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": 1755472222.914622, "relative_start": 5.8631041049957275, "end": 1755472222.914622, "relative_end": 1755472222.914622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f6b8d5c7f3ac3703ebfdef4449133ff4", "start": 1755472222.923131, "relative_start": 5.871613025665283, "end": 1755472222.923131, "relative_end": 1755472222.923131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f6b8d5c7f3ac3703ebfdef4449133ff4", "start": 1755472222.923785, "relative_start": 5.872267007827759, "end": 1755472222.923785, "relative_end": 1755472222.923785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": 1755472222.924287, "relative_start": 5.872769117355347, "end": 1755472222.924287, "relative_end": 1755472222.924287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a7781df99689693edd2c6c189d767089", "start": 1755472222.925661, "relative_start": 5.874143123626709, "end": 1755472222.925661, "relative_end": 1755472222.925661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::054b349d8fa17e9f8644145c2fbc1063", "start": 1755472222.926654, "relative_start": 5.875136137008667, "end": 1755472222.926654, "relative_end": 1755472222.926654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::054b349d8fa17e9f8644145c2fbc1063", "start": 1755472222.927096, "relative_start": 5.875577926635742, "end": 1755472222.927096, "relative_end": 1755472222.927096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6cb431528960518c12f7bad152d7b46d", "start": 1755472222.928049, "relative_start": 5.87653112411499, "end": 1755472222.928049, "relative_end": 1755472222.928049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": 1755472222.929439, "relative_start": 5.877921104431152, "end": 1755472222.929439, "relative_end": 1755472222.929439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.modal-authentication", "start": 1755472222.931916, "relative_start": 5.8803980350494385, "end": 1755472222.931916, "relative_end": 1755472222.931916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": 1755472222.953936, "relative_start": 5.90241813659668, "end": 1755472222.953936, "relative_end": 1755472222.953936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form-content-only", "start": 1755472222.958442, "relative_start": 5.90692400932312, "end": 1755472222.958442, "relative_end": 1755472222.958442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472222.959504, "relative_start": 5.9079859256744385, "end": 1755472222.959504, "relative_end": 1755472222.959504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472222.960868, "relative_start": 5.9093499183654785, "end": 1755472222.960868, "relative_end": 1755472222.960868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472222.962009, "relative_start": 5.910490989685059, "end": 1755472222.962009, "relative_end": 1755472222.962009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472222.962576, "relative_start": 5.911057949066162, "end": 1755472222.962576, "relative_end": 1755472222.962576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472222.963283, "relative_start": 5.911765098571777, "end": 1755472222.963283, "relative_end": 1755472222.963283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.email", "start": 1755472222.964024, "relative_start": 5.912506103515625, "end": 1755472222.964024, "relative_end": 1755472222.964024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1755472222.964891, "relative_start": 5.913372993469238, "end": 1755472222.964891, "relative_end": 1755472222.964891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472222.965846, "relative_start": 5.914328098297119, "end": 1755472222.965846, "relative_end": 1755472222.965846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472222.966886, "relative_start": 5.91536808013916, "end": 1755472222.966886, "relative_end": 1755472222.966886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472222.967265, "relative_start": 5.9157469272613525, "end": 1755472222.967265, "relative_end": 1755472222.967265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472222.967717, "relative_start": 5.916198968887329, "end": 1755472222.967717, "relative_end": 1755472222.967717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.password", "start": 1755472222.968441, "relative_start": 5.9169230461120605, "end": 1755472222.968441, "relative_end": 1755472222.968441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1755472222.969319, "relative_start": 5.917801141738892, "end": 1755472222.969319, "relative_end": 1755472222.969319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472222.969808, "relative_start": 5.918290138244629, "end": 1755472222.969808, "relative_end": 1755472222.969808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472222.970765, "relative_start": 5.919247150421143, "end": 1755472222.970765, "relative_end": 1755472222.970765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472222.971148, "relative_start": 5.91963005065918, "end": 1755472222.971148, "relative_end": 1755472222.971148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472222.97164, "relative_start": 5.920122146606445, "end": 1755472222.97164, "relative_end": 1755472222.97164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472222.972261, "relative_start": 5.920742988586426, "end": 1755472222.972261, "relative_end": 1755472222.972261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472222.972668, "relative_start": 5.921149969100952, "end": 1755472222.972668, "relative_end": 1755472222.972668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472222.973199, "relative_start": 5.921680927276611, "end": 1755472222.973199, "relative_end": 1755472222.973199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472222.973453, "relative_start": 5.921935081481934, "end": 1755472222.973453, "relative_end": 1755472222.973453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472222.973749, "relative_start": 5.922230958938599, "end": 1755472222.973749, "relative_end": 1755472222.973749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off-checkbox", "start": 1755472222.97431, "relative_start": 5.922791957855225, "end": 1755472222.97431, "relative_end": 1755472222.97431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off-checkbox", "start": 1755472222.975339, "relative_start": 5.923820972442627, "end": 1755472222.975339, "relative_end": 1755472222.975339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.on-off.checkbox", "start": 1755472222.976404, "relative_start": 5.924885988235474, "end": 1755472222.976404, "relative_end": 1755472222.976404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": 1755472222.97728, "relative_start": 5.925761938095093, "end": 1755472222.97728, "relative_end": 1755472222.97728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472222.978711, "relative_start": 5.92719292640686, "end": 1755472222.978711, "relative_end": 1755472222.978711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472222.979482, "relative_start": 5.927963972091675, "end": 1755472222.979482, "relative_end": 1755472222.979482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472222.979767, "relative_start": 5.92824912071228, "end": 1755472222.979767, "relative_end": 1755472222.979767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472222.980145, "relative_start": 5.928627014160156, "end": 1755472222.980145, "relative_end": 1755472222.980145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472222.980591, "relative_start": 5.929073095321655, "end": 1755472222.980591, "relative_end": 1755472222.980591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472222.981058, "relative_start": 5.929539918899536, "end": 1755472222.981058, "relative_end": 1755472222.981058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472222.981803, "relative_start": 5.9302849769592285, "end": 1755472222.981803, "relative_end": 1755472222.981803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472222.982072, "relative_start": 5.930554151535034, "end": 1755472222.982072, "relative_end": 1755472222.982072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472222.98265, "relative_start": 5.931132078170776, "end": 1755472222.98265, "relative_end": 1755472222.98265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472222.983116, "relative_start": 5.931597948074341, "end": 1755472222.983116, "relative_end": 1755472222.983116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472222.98354, "relative_start": 5.9320220947265625, "end": 1755472222.98354, "relative_end": 1755472222.98354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472222.984206, "relative_start": 5.932687997817993, "end": 1755472222.984206, "relative_end": 1755472222.984206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472222.984445, "relative_start": 5.932927131652832, "end": 1755472222.984445, "relative_end": 1755472222.984445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472222.984742, "relative_start": 5.9332239627838135, "end": 1755472222.984742, "relative_end": 1755472222.984742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472222.985097, "relative_start": 5.933578968048096, "end": 1755472222.985097, "relative_end": 1755472222.985097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472222.98547, "relative_start": 5.93395209312439, "end": 1755472222.98547, "relative_end": 1755472222.98547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472222.986019, "relative_start": 5.9345009326934814, "end": 1755472222.986019, "relative_end": 1755472222.986019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472222.98625, "relative_start": 5.934731960296631, "end": 1755472222.98625, "relative_end": 1755472222.98625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472222.986531, "relative_start": 5.935013055801392, "end": 1755472222.986531, "relative_end": 1755472222.986531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472222.986863, "relative_start": 5.935344934463501, "end": 1755472222.986863, "relative_end": 1755472222.986863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472222.987199, "relative_start": 5.935681104660034, "end": 1755472222.987199, "relative_end": 1755472222.987199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472222.987652, "relative_start": 5.936134099960327, "end": 1755472222.987652, "relative_end": 1755472222.987652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472222.987873, "relative_start": 5.936355113983154, "end": 1755472222.987873, "relative_end": 1755472222.987873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472222.988132, "relative_start": 5.936614036560059, "end": 1755472222.988132, "relative_end": 1755472222.988132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::button", "start": 1755472222.988724, "relative_start": 5.937206029891968, "end": 1755472222.988724, "relative_end": 1755472222.988724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472222.989237, "relative_start": 5.937719106674194, "end": 1755472222.989237, "relative_end": 1755472222.989237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472222.989561, "relative_start": 5.938043117523193, "end": 1755472222.989561, "relative_end": 1755472222.989561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472222.989898, "relative_start": 5.938380002975464, "end": 1755472222.989898, "relative_end": 1755472222.989898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472222.990355, "relative_start": 5.938837051391602, "end": 1755472222.990355, "relative_end": 1755472222.990355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472222.9906, "relative_start": 5.939082145690918, "end": 1755472222.9906, "relative_end": 1755472222.9906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472222.990903, "relative_start": 5.939384937286377, "end": 1755472222.990903, "relative_end": 1755472222.990903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472222.991228, "relative_start": 5.9397101402282715, "end": 1755472222.991228, "relative_end": 1755472222.991228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472222.991582, "relative_start": 5.940063953399658, "end": 1755472222.991582, "relative_end": 1755472222.991582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472222.992244, "relative_start": 5.940726041793823, "end": 1755472222.992244, "relative_end": 1755472222.992244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472222.992738, "relative_start": 5.941220045089722, "end": 1755472222.992738, "relative_end": 1755472222.992738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472222.993246, "relative_start": 5.941728115081787, "end": 1755472222.993246, "relative_end": 1755472222.993246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472222.993805, "relative_start": 5.942286968231201, "end": 1755472222.993805, "relative_end": 1755472222.993805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472222.994363, "relative_start": 5.942845106124878, "end": 1755472222.994363, "relative_end": 1755472222.994363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472222.995145, "relative_start": 5.943627119064331, "end": 1755472222.995145, "relative_end": 1755472222.995145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472222.995489, "relative_start": 5.9439709186553955, "end": 1755472222.995489, "relative_end": 1755472222.995489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472222.995939, "relative_start": 5.944421052932739, "end": 1755472222.995939, "relative_end": 1755472222.995939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472222.996431, "relative_start": 5.944913148880005, "end": 1755472222.996431, "relative_end": 1755472222.996431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472222.997255, "relative_start": 5.94573712348938, "end": 1755472222.997255, "relative_end": 1755472222.997255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472222.998184, "relative_start": 5.94666600227356, "end": 1755472222.998184, "relative_end": 1755472222.998184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472222.998586, "relative_start": 5.947067975997925, "end": 1755472222.998586, "relative_end": 1755472222.998586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472222.999042, "relative_start": 5.947524070739746, "end": 1755472222.999042, "relative_end": 1755472222.999042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.002431, "relative_start": 5.950912952423096, "end": **********.002431, "relative_end": **********.002431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.018738, "relative_start": 5.967220067977905, "end": **********.018738, "relative_end": **********.018738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::forms.auth", "start": **********.02175, "relative_start": 5.970232009887695, "end": **********.02175, "relative_end": **********.02175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2844accb98b3a6a2e1cb0b4f06fc3e6e", "start": **********.02387, "relative_start": 5.972352027893066, "end": **********.02387, "relative_end": **********.02387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.form-open-wrapper", "start": **********.024798, "relative_start": 5.97327995300293, "end": **********.024798, "relative_end": **********.024798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.025619, "relative_start": 5.9741010665893555, "end": **********.025619, "relative_end": **********.025619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.026208, "relative_start": 5.974689960479736, "end": **********.026208, "relative_end": **********.026208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.027117, "relative_start": 5.975599050521851, "end": **********.027117, "relative_end": **********.027117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.027576, "relative_start": 5.976058006286621, "end": **********.027576, "relative_end": **********.027576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.028131, "relative_start": 5.9766130447387695, "end": **********.028131, "relative_end": **********.028131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.02891, "relative_start": 5.977391958236694, "end": **********.02891, "relative_end": **********.02891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.029767, "relative_start": 5.9782490730285645, "end": **********.029767, "relative_end": **********.029767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.030386, "relative_start": 5.978868007659912, "end": **********.030386, "relative_end": **********.030386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.03123, "relative_start": 5.979712009429932, "end": **********.03123, "relative_end": **********.03123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.031591, "relative_start": 5.980072975158691, "end": **********.031591, "relative_end": **********.031591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.032024, "relative_start": 5.98050594329834, "end": **********.032024, "relative_end": **********.032024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.email", "start": **********.032612, "relative_start": 5.981094121932983, "end": **********.032612, "relative_end": **********.032612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.033354, "relative_start": 5.9818360805511475, "end": **********.033354, "relative_end": **********.033354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.033828, "relative_start": 5.982310056686401, "end": **********.033828, "relative_end": **********.033828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.034652, "relative_start": 5.983134031295776, "end": **********.034652, "relative_end": **********.034652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.034964, "relative_start": 5.98344612121582, "end": **********.034964, "relative_end": **********.034964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.035381, "relative_start": 5.983863115310669, "end": **********.035381, "relative_end": **********.035381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.password", "start": **********.035891, "relative_start": 5.984373092651367, "end": **********.035891, "relative_end": **********.035891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.036417, "relative_start": 5.984899044036865, "end": **********.036417, "relative_end": **********.036417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.036732, "relative_start": 5.985213994979858, "end": **********.036732, "relative_end": **********.036732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.03725, "relative_start": 5.985732078552246, "end": **********.03725, "relative_end": **********.03725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.037504, "relative_start": 5.985985994338989, "end": **********.037504, "relative_end": **********.037504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.037831, "relative_start": 5.986313104629517, "end": **********.037831, "relative_end": **********.037831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.0382, "relative_start": 5.986681938171387, "end": **********.0382, "relative_end": **********.0382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.038566, "relative_start": 5.987048149108887, "end": **********.038566, "relative_end": **********.038566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.039084, "relative_start": 5.987565994262695, "end": **********.039084, "relative_end": **********.039084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.039341, "relative_start": 5.987823009490967, "end": **********.039341, "relative_end": **********.039341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.039653, "relative_start": 5.988135099411011, "end": **********.039653, "relative_end": **********.039653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.040013, "relative_start": 5.988495111465454, "end": **********.040013, "relative_end": **********.040013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.040388, "relative_start": 5.988870143890381, "end": **********.040388, "relative_end": **********.040388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.0409, "relative_start": 5.989382028579712, "end": **********.0409, "relative_end": **********.0409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.041145, "relative_start": 5.989627122879028, "end": **********.041145, "relative_end": **********.041145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.041428, "relative_start": 5.989910125732422, "end": **********.041428, "relative_end": **********.041428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::button", "start": **********.041863, "relative_start": 5.990345001220703, "end": **********.041863, "relative_end": **********.041863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.042414, "relative_start": 5.990895986557007, "end": **********.042414, "relative_end": **********.042414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.043098, "relative_start": 5.991580009460449, "end": **********.043098, "relative_end": **********.043098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.043613, "relative_start": 5.992094993591309, "end": **********.043613, "relative_end": **********.043613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.044418, "relative_start": 5.992900133132935, "end": **********.044418, "relative_end": **********.044418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.044883, "relative_start": 5.993365049362183, "end": **********.044883, "relative_end": **********.044883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.045721, "relative_start": 5.994203090667725, "end": **********.045721, "relative_end": **********.045721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.04622, "relative_start": 5.994702100753784, "end": **********.04622, "relative_end": **********.04622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.046749, "relative_start": 5.9952311515808105, "end": **********.046749, "relative_end": **********.046749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.047645, "relative_start": 5.996127128601074, "end": **********.047645, "relative_end": **********.047645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.048205, "relative_start": 5.996686935424805, "end": **********.048205, "relative_end": **********.048205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.048967, "relative_start": 5.997448921203613, "end": **********.048967, "relative_end": **********.048967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.04964, "relative_start": 5.998121976852417, "end": **********.04964, "relative_end": **********.04964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.05052, "relative_start": 5.999001979827881, "end": **********.05052, "relative_end": **********.05052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.051313, "relative_start": 5.999794960021973, "end": **********.051313, "relative_end": **********.051313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.051648, "relative_start": 6.00012993812561, "end": **********.051648, "relative_end": **********.051648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.052087, "relative_start": 6.000569105148315, "end": **********.052087, "relative_end": **********.052087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.052581, "relative_start": 6.001063108444214, "end": **********.052581, "relative_end": **********.052581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.053103, "relative_start": 6.001585006713867, "end": **********.053103, "relative_end": **********.053103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.053787, "relative_start": 6.00226902961731, "end": **********.053787, "relative_end": **********.053787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.054096, "relative_start": 6.002578020095825, "end": **********.054096, "relative_end": **********.054096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.054469, "relative_start": 6.002951145172119, "end": **********.054469, "relative_end": **********.054469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.05918, "relative_start": 6.007662057876587, "end": **********.05918, "relative_end": **********.05918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.footer", "start": **********.062173, "relative_start": 6.010654926300049, "end": **********.062173, "relative_end": **********.062173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": **********.064801, "relative_start": 6.013283014297485, "end": **********.064801, "relative_end": **********.064801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::layouts.base", "start": **********.065567, "relative_start": 6.014049053192139, "end": **********.065567, "relative_end": **********.065567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.header", "start": **********.073251, "relative_start": 6.021733045578003, "end": **********.073251, "relative_end": **********.073251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language::partials.hreflang", "start": **********.433778, "relative_start": 6.382260084152222, "end": **********.433778, "relative_end": **********.433778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.footer", "start": **********.439877, "relative_start": 6.388359069824219, "end": **********.439877, "relative_end": **********.439877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::fronts.toast-notification", "start": **********.453355, "relative_start": 6.401837110519409, "end": **********.453355, "relative_end": **********.453355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.465081, "relative_start": 6.413563013076782, "end": **********.46532, "relative_end": **********.46532, "duration": 0.0002391338348388672, "duration_str": "239μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 67148904, "peak_usage_str": "64MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 183, "nb_templates": 183, "templates": [{"name": "1x theme.xmetr::views.real-estate.property", "param_count": null, "params": [], "start": **********.32678, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/property.blade.phptheme.xmetr::views.real-estate.property", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fproperty.blade.php:1", "ajax": false, "filename": "property.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.property"}, {"name": "1x theme.xmetr::views.real-estate.single-layouts.style-5", "param_count": null, "params": [], "start": **********.587959, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/style-5.blade.phptheme.xmetr::views.real-estate.single-layouts.style-5", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fsingle-layouts%2Fstyle-5.blade.php:1", "ajax": false, "filename": "style-5.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.single-layouts.style-5"}, {"name": "1x theme.xmetr::views.real-estate.single-layouts.partials.gallery-grid", "param_count": null, "params": [], "start": **********.588984, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/gallery-grid.blade.phptheme.xmetr::views.real-estate.single-layouts.partials.gallery-grid", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fsingle-layouts%2Fpartials%2Fgallery-grid.blade.php:1", "ajax": false, "filename": "gallery-grid.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.single-layouts.partials.gallery-grid"}, {"name": "1x theme.xmetr::views.real-estate.single-layouts.partials.description", "param_count": null, "params": [], "start": **********.900814, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/description.blade.phptheme.xmetr::views.real-estate.single-layouts.partials.description", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fsingle-layouts%2Fpartials%2Fdescription.blade.php:1", "ajax": false, "filename": "description.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.single-layouts.partials.description"}, {"name": "1x theme.xmetr::views.real-estate.single-layouts.partials.features", "param_count": null, "params": [], "start": **********.909466, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/features.blade.phptheme.xmetr::views.real-estate.single-layouts.partials.features", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fsingle-layouts%2Fpartials%2Ffeatures.blade.php:1", "ajax": false, "filename": "features.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.single-layouts.partials.features"}, {"name": "1x theme.xmetr::views.real-estate.single-layouts.partials.facilities", "param_count": null, "params": [], "start": **********.915898, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/facilities.blade.phptheme.xmetr::views.real-estate.single-layouts.partials.facilities", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fsingle-layouts%2Fpartials%2Ffacilities.blade.php:1", "ajax": false, "filename": "facilities.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.single-layouts.partials.facilities"}, {"name": "1x theme.xmetr::views.real-estate.single-layouts.partials.video-gallery", "param_count": null, "params": [], "start": **********.918805, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/video-gallery.blade.phptheme.xmetr::views.real-estate.single-layouts.partials.video-gallery", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fsingle-layouts%2Fpartials%2Fvideo-gallery.blade.php:1", "ajax": false, "filename": "video-gallery.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.single-layouts.partials.video-gallery"}, {"name": "1x theme.xmetr::views.real-estate.single-layouts.partials.rental-conditions", "param_count": null, "params": [], "start": **********.919439, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/rental-conditions.blade.phptheme.xmetr::views.real-estate.single-layouts.partials.rental-conditions", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fsingle-layouts%2Fpartials%2Frental-conditions.blade.php:1", "ajax": false, "filename": "rental-conditions.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.single-layouts.partials.rental-conditions"}, {"name": "1x theme.xmetr::views.real-estate.single-layouts.partials.district-ratings", "param_count": null, "params": [], "start": **********.924091, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/district-ratings.blade.phptheme.xmetr::views.real-estate.single-layouts.partials.district-ratings", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fsingle-layouts%2Fpartials%2Fdistrict-ratings.blade.php:1", "ajax": false, "filename": "district-ratings.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.single-layouts.partials.district-ratings"}, {"name": "1x theme.xmetr::views.real-estate.single-layouts.partials.map", "param_count": null, "params": [], "start": **********.924957, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/map.blade.phptheme.xmetr::views.real-estate.single-layouts.partials.map", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fsingle-layouts%2Fpartials%2Fmap.blade.php:1", "ajax": false, "filename": "map.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.single-layouts.partials.map"}, {"name": "1x theme.xmetr::views.real-estate.single-layouts.partials.project", "param_count": null, "params": [], "start": **********.929109, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/project.blade.phptheme.xmetr::views.real-estate.single-layouts.partials.project", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fsingle-layouts%2Fpartials%2Fproject.blade.php:1", "ajax": false, "filename": "project.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.single-layouts.partials.project"}, {"name": "1x theme.xmetr::views.real-estate.single-layouts.partials.agent", "param_count": null, "params": [], "start": **********.930135, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/agent.blade.phptheme.xmetr::views.real-estate.single-layouts.partials.agent", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fsingle-layouts%2Fpartials%2Fagent.blade.php:1", "ajax": false, "filename": "agent.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.single-layouts.partials.agent"}, {"name": "1x theme.xmetr::views.real-estate.single-layouts.partials.related-properties", "param_count": null, "params": [], "start": **********.956897, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/related-properties.blade.phptheme.xmetr::views.real-estate.single-layouts.partials.related-properties", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fsingle-layouts%2Fpartials%2Frelated-properties.blade.php:1", "ajax": false, "filename": "related-properties.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.single-layouts.partials.related-properties"}, {"name": "3x theme.xmetr::views.real-estate.properties.item-grid", "param_count": null, "params": [], "start": **********.009016, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.phptheme.xmetr::views.real-estate.properties.item-grid", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fproperties%2Fitem-grid.blade.php:1", "ajax": false, "filename": "item-grid.blade.php", "line": "?"}, "render_count": 3, "name_original": "theme.xmetr::views.real-estate.properties.item-grid"}, {"name": "1x __components::dd248739db8ba923ebfe1f426bb71a38", "param_count": null, "params": [], "start": **********.049918, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/dd248739db8ba923ebfe1f426bb71a38.blade.php__components::dd248739db8ba923ebfe1f426bb71a38", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fdd248739db8ba923ebfe1f426bb71a38.blade.php:1", "ajax": false, "filename": "dd248739db8ba923ebfe1f426bb71a38.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dd248739db8ba923ebfe1f426bb71a38"}, {"name": "1x __components::5def649a5a47936dda7e47db6ffcfa75", "param_count": null, "params": [], "start": **********.065872, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/5def649a5a47936dda7e47db6ffcfa75.blade.php__components::5def649a5a47936dda7e47db6ffcfa75", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F5def649a5a47936dda7e47db6ffcfa75.blade.php:1", "ajax": false, "filename": "5def649a5a47936dda7e47db6ffcfa75.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5def649a5a47936dda7e47db6ffcfa75"}, {"name": "1x __components::de8568c69a934121dc0e6e0a5eccedeb", "param_count": null, "params": [], "start": **********.067831, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/de8568c69a934121dc0e6e0a5eccedeb.blade.php__components::de8568c69a934121dc0e6e0a5eccedeb", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fde8568c69a934121dc0e6e0a5eccedeb.blade.php:1", "ajax": false, "filename": "de8568c69a934121dc0e6e0a5eccedeb.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::de8568c69a934121dc0e6e0a5eccedeb"}, {"name": "1x __components::1c56464a7585811c54c34ced5fe63e79", "param_count": null, "params": [], "start": **********.070161, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/1c56464a7585811c54c34ced5fe63e79.blade.php__components::1c56464a7585811c54c34ced5fe63e79", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F1c56464a7585811c54c34ced5fe63e79.blade.php:1", "ajax": false, "filename": "1c56464a7585811c54c34ced5fe63e79.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1c56464a7585811c54c34ced5fe63e79"}, {"name": "1x theme.xmetr::layouts.full-width", "param_count": null, "params": [], "start": **********.073027, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/layouts/full-width.blade.phptheme.xmetr::layouts.full-width", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Flayouts%2Ffull-width.blade.php:1", "ajax": false, "filename": "full-width.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::layouts.full-width"}, {"name": "4x plugins/ads::partials.ad-display", "param_count": null, "params": [], "start": **********.136522, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/ads/resources/views/partials/ad-display.blade.phpplugins/ads::partials.ad-display", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Fads%2Fresources%2Fviews%2Fpartials%2Fad-display.blade.php:1", "ajax": false, "filename": "ad-display.blade.php", "line": "?"}, "render_count": 4, "name_original": "plugins/ads::partials.ad-display"}, {"name": "1x theme.xmetr::partials.header", "param_count": null, "params": [], "start": **********.138004, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/header.blade.phptheme.xmetr::partials.header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.header"}, {"name": "4x plugins/social-login::login-options", "param_count": null, "params": [], "start": **********.189579, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/social-login/resources/views/login-options.blade.phpplugins/social-login::login-options", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Fsocial-login%2Fresources%2Fviews%2Flogin-options.blade.php:1", "ajax": false, "filename": "login-options.blade.php", "line": "?"}, "render_count": 4, "name_original": "plugins/social-login::login-options"}, {"name": "1x theme.xmetr::partials.breadcrumb", "param_count": null, "params": [], "start": 1755472222.878262, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/breadcrumb.blade.phptheme.xmetr::partials.breadcrumb", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Fbreadcrumb.blade.php:1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.breadcrumb"}, {"name": "2x __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "param_count": null, "params": [], "start": 1755472222.905275, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php__components::98e88d58787b8dfeb6f0d1dc0a785cfd", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php:1", "ajax": false, "filename": "98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::98e88d58787b8dfeb6f0d1dc0a785cfd"}, {"name": "3x __components::054b349d8fa17e9f8644145c2fbc1063", "param_count": null, "params": [], "start": 1755472222.907323, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/054b349d8fa17e9f8644145c2fbc1063.blade.php__components::054b349d8fa17e9f8644145c2fbc1063", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F054b349d8fa17e9f8644145c2fbc1063.blade.php:1", "ajax": false, "filename": "054b349d8fa17e9f8644145c2fbc1063.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::054b349d8fa17e9f8644145c2fbc1063"}, {"name": "2x __components::6cb431528960518c12f7bad152d7b46d", "param_count": null, "params": [], "start": 1755472222.911408, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/6cb431528960518c12f7bad152d7b46d.blade.php__components::6cb431528960518c12f7bad152d7b46d", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F6cb431528960518c12f7bad152d7b46d.blade.php:1", "ajax": false, "filename": "6cb431528960518c12f7bad152d7b46d.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::6cb431528960518c12f7bad152d7b46d"}, {"name": "2x __components::f6b8d5c7f3ac3703ebfdef4449133ff4", "param_count": null, "params": [], "start": 1755472222.923094, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/f6b8d5c7f3ac3703ebfdef4449133ff4.blade.php__components::f6b8d5c7f3ac3703ebfdef4449133ff4", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ff6b8d5c7f3ac3703ebfdef4449133ff4.blade.php:1", "ajax": false, "filename": "f6b8d5c7f3ac3703ebfdef4449133ff4.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::f6b8d5c7f3ac3703ebfdef4449133ff4"}, {"name": "1x __components::a7781df99689693edd2c6c189d767089", "param_count": null, "params": [], "start": 1755472222.925623, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/a7781df99689693edd2c6c189d767089.blade.php__components::a7781df99689693edd2c6c189d767089", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fa7781df99689693edd2c6c189d767089.blade.php:1", "ajax": false, "filename": "a7781df99689693edd2c6c189d767089.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a7781df99689693edd2c6c189d767089"}, {"name": "1x theme.xmetr::partials.modal-authentication", "param_count": null, "params": [], "start": 1755472222.931895, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/modal-authentication.blade.phptheme.xmetr::partials.modal-authentication", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Fmodal-authentication.blade.php:1", "ajax": false, "filename": "modal-authentication.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.modal-authentication"}, {"name": "4x core/js-validation::bootstrap", "param_count": null, "params": [], "start": 1755472222.953912, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php:1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/js-validation::bootstrap"}, {"name": "1x core/base::forms.form-content-only", "param_count": null, "params": [], "start": 1755472222.958401, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form-content-only.blade.phpcore/base::forms.form-content-only", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform-content-only.blade.php:1", "ajax": false, "filename": "form-content-only.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form-content-only"}, {"name": "17x core/base::forms.fields.html", "param_count": null, "params": [], "start": 1755472222.959461, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/html.blade.phpcore/base::forms.fields.html", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fhtml.blade.php:1", "ajax": false, "filename": "html.blade.php", "line": "?"}, "render_count": 17, "name_original": "core/base::forms.fields.html"}, {"name": "23x ********************************::form.field", "param_count": null, "params": [], "start": 1755472222.960835, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/field.blade.php********************************::form.field", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php:1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 23, "name_original": "********************************::form.field"}, {"name": "23x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": 1755472222.961976, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php:1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 23, "name_original": "core/base::forms.partials.help-block"}, {"name": "23x core/base::forms.partials.errors", "param_count": null, "params": [], "start": 1755472222.962545, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php:1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 23, "name_original": "core/base::forms.partials.errors"}, {"name": "25x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": 1755472222.963252, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php:1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 25, "name_original": "core/base::forms.columns.column-span"}, {"name": "2x core/base::forms.fields.email", "param_count": null, "params": [], "start": 1755472222.963991, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/email.blade.phpcore/base::forms.fields.email", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Femail.blade.php:1", "ajax": false, "filename": "email.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.email"}, {"name": "5x core/base::forms.partials.label", "param_count": null, "params": [], "start": 1755472222.964855, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/label.blade.phpcore/base::forms.partials.label", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Flabel.blade.php:1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 5, "name_original": "core/base::forms.partials.label"}, {"name": "2x core/base::forms.fields.password", "param_count": null, "params": [], "start": 1755472222.968409, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/password.blade.phpcore/base::forms.fields.password", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fpassword.blade.php:1", "ajax": false, "filename": "password.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.password"}, {"name": "1x core/base::forms.fields.on-off-checkbox", "param_count": null, "params": [], "start": 1755472222.974268, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/on-off-checkbox.blade.phpcore/base::forms.fields.on-off-checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fon-off-checkbox.blade.php:1", "ajax": false, "filename": "on-off-checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.on-off-checkbox"}, {"name": "1x core/base::forms.partials.on-off-checkbox", "param_count": null, "params": [], "start": 1755472222.975304, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/on-off-checkbox.blade.phpcore/base::forms.partials.on-off-checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fon-off-checkbox.blade.php:1", "ajax": false, "filename": "on-off-checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.on-off-checkbox"}, {"name": "1x ********************************::form.on-off.checkbox", "param_count": null, "params": [], "start": 1755472222.976369, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/on-off/checkbox.blade.php********************************::form.on-off.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fon-off%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.on-off.checkbox"}, {"name": "1x core/base::components.form.checkbox", "param_count": null, "params": [], "start": 1755472222.977241, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.phpcore/base::components.form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.form.checkbox"}, {"name": "2x laravel-form-builder::button", "param_count": null, "params": [], "start": 1755472222.988705, "type": "php", "hash": "phpD:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src/../resources/views/button.phplaravel-form-builder::button", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fform-builder%2Fresources%2Fviews%2Fbutton.php:1", "ajax": false, "filename": "button.php", "line": "?"}, "render_count": 2, "name_original": "laravel-form-builder::button"}, {"name": "1x plugins/real-estate::forms.auth", "param_count": null, "params": [], "start": **********.021703, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/forms/auth.blade.phpplugins/real-estate::forms.auth", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fforms%2Fauth.blade.php:1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::forms.auth"}, {"name": "1x __components::2844accb98b3a6a2e1cb0b4f06fc3e6e", "param_count": null, "params": [], "start": **********.023823, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/2844accb98b3a6a2e1cb0b4f06fc3e6e.blade.php__components::2844accb98b3a6a2e1cb0b4f06fc3e6e", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F2844accb98b3a6a2e1cb0b4f06fc3e6e.blade.php:1", "ajax": false, "filename": "2844accb98b3a6a2e1cb0b4f06fc3e6e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2844accb98b3a6a2e1cb0b4f06fc3e6e"}, {"name": "1x core/base::forms.columns.form-open-wrapper", "param_count": null, "params": [], "start": **********.024758, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/columns/form-open-wrapper.blade.phpcore/base::forms.columns.form-open-wrapper", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fform-open-wrapper.blade.php:1", "ajax": false, "filename": "form-open-wrapper.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.columns.form-open-wrapper"}, {"name": "1x core/base::forms.fields.text", "param_count": null, "params": [], "start": **********.028879, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/text.blade.phpcore/base::forms.fields.text", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftext.blade.php:1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.text"}, {"name": "1x theme.xmetr::partials.footer", "param_count": null, "params": [], "start": **********.062134, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/footer.blade.phptheme.xmetr::partials.footer", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Ffooter.blade.php:1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.footer"}, {"name": "1x theme.xmetr::layouts.base", "param_count": null, "params": [], "start": **********.06553, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/layouts/base.blade.phptheme.xmetr::layouts.base", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Flayouts%2Fbase.blade.php:1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::layouts.base"}, {"name": "1x packages/theme::partials.header", "param_count": null, "params": [], "start": **********.073228, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/theme/resources/views/partials/header.blade.phppackages/theme::partials.header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.header"}, {"name": "1x plugins/language::partials.hreflang", "param_count": null, "params": [], "start": **********.433738, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/language/resources/views/partials/hreflang.blade.phpplugins/language::partials.hreflang", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fresources%2Fviews%2Fpartials%2Fhreflang.blade.php:1", "ajax": false, "filename": "hreflang.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/language::partials.hreflang"}, {"name": "1x packages/theme::partials.footer", "param_count": null, "params": [], "start": **********.439839, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/theme/resources/views/partials/footer.blade.phppackages/theme::partials.footer", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Ffooter.blade.php:1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.footer"}, {"name": "1x packages/theme::fronts.toast-notification", "param_count": null, "params": [], "start": **********.453313, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/theme/resources/views/fronts/toast-notification.blade.phppackages/theme::fronts.toast-notification", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Ffronts%2Ftoast-notification.blade.php:1", "ajax": false, "filename": "toast-notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::fronts.toast-notification"}]}, "queries": {"count": 69, "nb_statements": 69, "nb_visible_statements": 69, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.22871999999999998, "accumulated_duration_str": "229ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `slugs_translations` where `lang_code` = 'en' and `key` = 'long-term-rental-of-a-2-room-apartment-48m2-krakow-poland' limit 1", "type": "query", "params": [], "bindings": ["en", "long-term-rental-of-a-2-room-apartment-48m2-krakow-poland"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 98}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.04022, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "RedirectIncorrectLanguagePrefix.php:98", "source": {"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 98}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fapp%2FHttp%2FMiddleware%2FRedirectIncorrectLanguagePrefix.php:98", "ajax": false, "filename": "RedirectIncorrectLanguagePrefix.php", "line": "98"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 0.184}, {"sql": "select * from `slugs` where `key` = 'long-term-rental-of-a-2-room-apartment-48m2-krakow-poland' limit 1", "type": "query", "params": [], "bindings": ["long-term-rental-of-a-2-room-apartment-48m2-krakow-poland"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 107}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.042473, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "RedirectIncorrectLanguagePrefix.php:107", "source": {"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 107}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fapp%2FHttp%2FMiddleware%2FRedirectIncorrectLanguagePrefix.php:107", "ajax": false, "filename": "RedirectIncorrectLanguagePrefix.php", "line": "107"}, "connection": "xmetr", "explain": null, "start_percent": 0.184, "width_percent": 0.118}, {"sql": "select * from `slugs` where (`key` = 'long-term-rental-of-a-2-room-apartment-48m2-krakow-poland' and `prefix` = 'properties') or exists (select * from `slugs_translations` where `slugs`.`id` = `slugs_translations`.`slugs_id` and (`key` = 'long-term-rental-of-a-2-room-apartment-48m2-krakow-poland' and `prefix` = 'properties')) limit 1", "type": "query", "params": [], "bindings": ["long-term-rental-of-a-2-room-apartment-48m2-krakow-poland", "properties", "long-term-rental-of-a-2-room-apartment-48m2-krakow-poland", "properties"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/SlugHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\SlugHelper.php", "line": 182}, {"index": 19, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 116}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.049525, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0.302, "width_percent": 0.249}, {"sql": "select * from `re_properties` where (`id` = 1229 and `re_properties`.`moderation_status` = 'approved' and `re_properties`.`status` != 'not_available') limit 1", "type": "query", "params": [], "bindings": [1229, "approved", "not_available"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Services/HandleFrontPages.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\HandleFrontPages.php", "line": 51}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 798}, {"index": 23, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 25, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 116}], "start": **********.055034, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0.551, "width_percent": 0.341}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `slugs`.`reference_id` = 1229 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1229], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Services/HandleFrontPages.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\HandleFrontPages.php", "line": 53}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 798}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.062192, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0.892, "width_percent": 0.21}, {"sql": "select `id`, `name` from `pages` where `status` = 'published' and `id` = '14' limit 1", "type": "query", "params": [], "bindings": ["published", "14"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Services/HandleFrontPages.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\HandleFrontPages.php", "line": 79}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 798}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 116}], "start": **********.2728481, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 1.102, "width_percent": 0.38}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (14) and `slugs`.`reference_type` = 'Xmetr\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Xmetr\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Services/HandleFrontPages.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\HandleFrontPages.php", "line": 79}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 798}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.276206, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 1.482, "width_percent": 0.201}, {"sql": "select * from `cities` where `cities`.`id` = 8205 limit 1", "type": "query", "params": [], "bindings": [8205], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Services/HandleFrontPages.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\HandleFrontPages.php", "line": 83}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 798}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.284364, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 1.683, "width_percent": 0.175}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Services/HandleFrontPages.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\HandleFrontPages.php", "line": 83}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 798}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.286318, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 1.858, "width_percent": 0.127}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Xmetr\\\\Location\\\\Models\\\\Country' and `slugs`.`reference_id` = 29 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Xmetr\\Location\\Models\\Country", 29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": null, "name": "platform/plugins/real-estate/src/Services/HandleFrontPages.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\HandleFrontPages.php", "line": 83}], "start": **********.290008, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 1.985, "width_percent": 0.306}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Xmetr\\\\Location\\\\Models\\\\City' and `slugs`.`reference_id` = 8205 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Xmetr\\Location\\Models\\City", 8205], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": null, "name": "platform/plugins/real-estate/src/Services/HandleFrontPages.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\HandleFrontPages.php", "line": 84}], "start": **********.298374, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 2.291, "width_percent": 0.179}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (1229) and `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property'", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/packages/seo-helper/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\Providers\\HookServiceProvider.php", "line": 87}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 30, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 798}, {"index": 34, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.306503, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 2.47, "width_percent": 0.533}, {"sql": "select * from `re_currencies` where `re_currencies`.`id` = 36 limit 1", "type": "query", "params": [], "bindings": [36], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 264}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": "view", "name": "theme.xmetr::views.real-estate.property", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/property.blade.php", "line": 19}], "start": **********.3298259, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 3.004, "width_percent": 0.179}, {"sql": "select * from `re_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/helpers/currencies.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\currencies.php", "line": 142}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.333186, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 3.183, "width_percent": 0.245}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.property", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/property.blade.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.445678, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 3.428, "width_percent": 0.56}, {"sql": "select `re_categories`.*, `re_property_categories`.`property_id` as `pivot_property_id`, `re_property_categories`.`category_id` as `pivot_category_id` from `re_categories` inner join `re_property_categories` on `re_categories`.`id` = `re_property_categories`.`category_id` where `re_property_categories`.`property_id` = 1229", "type": "query", "params": [], "bindings": [1229], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.property", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/property.blade.php", "line": 22}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.456778, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 3.987, "width_percent": 1.229}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 154 limit 1", "type": "query", "params": [], "bindings": [154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.property", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/property.blade.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.5777678, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 5.216, "width_percent": 0.398}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 154 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.property", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/property.blade.php", "line": 42}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.5810502, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 5.614, "width_percent": 0.118}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `slugs`.`reference_id` = 154 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 154], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": "view", "name": "theme.xmetr::views.real-estate.property", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/property.blade.php", "line": 46}], "start": **********.58468, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 5.732, "width_percent": 0.127}, {"sql": "select * from `re_property_daily_views` where `property_id` = 1229 and `view_date` = '2025-08-17' limit 1", "type": "query", "params": [], "bindings": [1229, "2025-08-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Models/PropertyDailyView.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\PropertyDailyView.php", "line": 58}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 378}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 388}, {"index": 26, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.80819, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 5.859, "width_percent": 0.201}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1229", "type": "query", "params": [], "bindings": [1229], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.single-layouts.partials.features", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/features.blade.php", "line": 1}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.9105651, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 6.06, "width_percent": 0.144}, {"sql": "select `re_facilities`.*, `re_facilities_distances`.`reference_id` as `pivot_reference_id`, `re_facilities_distances`.`facility_id` as `pivot_facility_id`, `re_facilities_distances`.`reference_type` as `pivot_reference_type`, `re_facilities_distances`.`distance` as `pivot_distance` from `re_facilities` inner join `re_facilities_distances` on `re_facilities`.`id` = `re_facilities_distances`.`facility_id` where `re_facilities_distances`.`reference_id` = 1229 and `re_facilities_distances`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property'", "type": "query", "params": [], "bindings": [1229, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.single-layouts.partials.facilities", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/facilities.blade.php", "line": 1}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.917553, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 6.204, "width_percent": 0.188}, {"sql": "select * from `media_files` where `media_files`.`id` = 9027 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9027], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.single-layouts.partials.agent", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/agent.blade.php", "line": 7}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.933076, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 6.392, "width_percent": 0.162}, {"sql": "select `re_spoken_languages`.*, `re_account_spoken_languages`.`account_id` as `pivot_account_id`, `re_account_spoken_languages`.`spoken_language_id` as `pivot_spoken_language_id` from `re_spoken_languages` inner join `re_account_spoken_languages` on `re_spoken_languages`.`id` = `re_account_spoken_languages`.`spoken_language_id` where `re_account_spoken_languages`.`account_id` = 154", "type": "query", "params": [], "bindings": [154], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.single-layouts.partials.agent", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/agent.blade.php", "line": 23}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.9391742, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 6.554, "width_percent": 0.192}, {"sql": "select count(*) as aggregate from `re_properties` where `re_properties`.`author_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `re_properties`.`author_id` = 154 and `re_properties`.`author_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 154], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Account.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Account.php", "line": 282}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 26, "namespace": "view", "name": "theme.xmetr::views.real-estate.single-layouts.partials.agent", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/agent.blade.php", "line": 35}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.94929, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Account.php:282", "source": {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Account.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Account.php", "line": 282}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FAccount.php:282", "ajax": false, "filename": "Account.php", "line": "282"}, "connection": "xmetr", "explain": null, "start_percent": 6.746, "width_percent": 0.14}, {"sql": "select * from `re_accounts` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "view", "name": "theme.xmetr::views.real-estate.single-layouts.partials.agent", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/agent.blade.php", "line": 52}], "start": **********.952052, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 6.886, "width_percent": 0.359}, {"sql": "select * from `re_properties` where `id` = 1229 limit 1", "type": "query", "params": [], "bindings": [1229], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 52}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 24}, {"index": 19, "namespace": "view", "name": "theme.xmetr::views.real-estate.single-layouts.partials.related-properties", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/related-properties.blade.php", "line": 3}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.962804, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 7.245, "width_percent": 0.24}, {"sql": "select `re_categories`.*, `re_property_categories`.`property_id` as `pivot_property_id`, `re_property_categories`.`category_id` as `pivot_category_id` from `re_categories` inner join `re_property_categories` on `re_categories`.`id` = `re_property_categories`.`category_id` where `re_property_categories`.`property_id` in (1229)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 52}, {"index": 22, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 24}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.single-layouts.partials.related-properties", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/related-properties.blade.php", "line": 3}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.96487, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 7.485, "width_percent": 0.131}, {"sql": "select * from `re_properties` where `id` <> 1229 and (`re_properties`.`moderation_status` = 'approved' and `re_properties`.`status` != 'not_available') and (`expire_date` >= '2025-08-17 23:10:18' or `never_expired` = 1) and exists (select * from `re_categories` inner join `re_property_categories` on `re_categories`.`id` = `re_property_categories`.`category_id` where `re_properties`.`id` = `re_property_categories`.`property_id` and `category_id` in (7)) and `type` = 'rent' and `city_id` = 8205 and `price` between 2590 and 4810 order by RAND(), `created_at` desc limit 3", "type": "query", "params": [], "bindings": [1229, "approved", "not_available", "2025-08-17 23:10:18", 1, 7, {"value": "rent", "label": "Rent"}, 8205, 2590, 4810], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 60}, {"index": 18, "namespace": "view", "name": "theme.xmetr::views.real-estate.single-layouts.partials.related-properties", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/related-properties.blade.php", "line": 3}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.9691792, "duration": 0.0182, "duration_str": "18.2ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 7.616, "width_percent": 7.957}, {"sql": "select `id`, `key`, `prefix`, `reference_id` from `slugs` where `slugs`.`reference_id` in (353, 740, 960) and `slugs`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property'", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 60}, {"index": 24, "namespace": "view", "name": "theme.xmetr::views.real-estate.single-layouts.partials.related-properties", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/related-properties.blade.php", "line": 3}], "start": **********.989937, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 15.574, "width_percent": 0.621}, {"sql": "select `id`, `name` from `states` where `states`.`id` in (197)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 60}, {"index": 24, "namespace": "view", "name": "theme.xmetr::views.real-estate.single-layouts.partials.related-properties", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/related-properties.blade.php", "line": 3}], "start": **********.996473, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 16.194, "width_percent": 0.192}, {"sql": "select `id`, `name` from `cities` where `cities`.`id` in (8205)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 60}, {"index": 24, "namespace": "view", "name": "theme.xmetr::views.real-estate.single-layouts.partials.related-properties", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/related-properties.blade.php", "line": 3}], "start": **********.0001912, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 16.387, "width_percent": 0.122}, {"sql": "select `id`, `is_default`, `exchange_rate`, `symbol`, `title`, `is_prefix_symbol` from `re_currencies` where `re_currencies`.`id` in (36)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 60}, {"index": 24, "namespace": "view", "name": "theme.xmetr::views.real-estate.single-layouts.partials.related-properties", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/related-properties.blade.php", "line": 3}], "start": **********.002156, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 16.509, "width_percent": 0.118}, {"sql": "select `re_categories`.`id`, `re_categories`.`name`, `re_property_categories`.`property_id` as `pivot_property_id`, `re_property_categories`.`category_id` as `pivot_category_id` from `re_categories` inner join `re_property_categories` on `re_categories`.`id` = `re_property_categories`.`category_id` where `re_property_categories`.`property_id` in (353, 740, 960) and `status` = 'published' order by `created_at` desc, `is_default` desc, `order` desc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 21, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 60}, {"index": 22, "namespace": "view", "name": "theme.xmetr::views.real-estate.single-layouts.partials.related-properties", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/related-properties.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.0045671, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 16.627, "width_percent": 0.603}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 353 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 353], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.208845, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 17.231, "width_percent": 0.162}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 152}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.319071, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 17.392, "width_percent": 0.149}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 353", "type": "query", "params": [], "bindings": [353], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 158}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.323339, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 17.541, "width_percent": 0.254}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 8 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.327401, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 17.795, "width_percent": 0.166}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 10 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.332935, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 17.961, "width_percent": 0.144}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 13 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 13], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.336077, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 18.105, "width_percent": 0.136}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.339301, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 18.241, "width_percent": 0.136}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 16 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.345646, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 18.376, "width_percent": 0.166}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 68 limit 1", "type": "query", "params": [], "bindings": [68], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 199}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3521612, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 18.542, "width_percent": 0.516}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 68 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 68], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 252}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.3602662, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 19.058, "width_percent": 0.424}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 960 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 960], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.503228, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 19.482, "width_percent": 0.612}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 152}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6275568, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 20.094, "width_percent": 0.258}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 960", "type": "query", "params": [], "bindings": [960], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 158}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.631687, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 20.352, "width_percent": 0.984}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.63552, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 21.336, "width_percent": 0.127}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 16 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.639532, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 21.463, "width_percent": 0.14}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 98 limit 1", "type": "query", "params": [], "bindings": [98], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 199}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.645015, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 21.603, "width_percent": 0.35}, {"sql": "select * from `media_files` where `media_files`.`id` = 4600 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4600], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 206}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.647392, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 21.953, "width_percent": 0.131}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 98 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 98], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 252}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.651081, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 22.084, "width_percent": 0.122}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 740 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 740], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.809289, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 22.206, "width_percent": 0.494}, {"sql": "select * from `countries` where `countries`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 152}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.914284, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 22.7, "width_percent": 0.223}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 740", "type": "query", "params": [], "bindings": [740], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 158}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.918364, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 22.923, "width_percent": 1.067}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 8 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.923836, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 23.99, "width_percent": 0.245}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.93088, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 24.235, "width_percent": 0.73}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 13 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 13], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.940672, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 24.965, "width_percent": 0.245}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 98 limit 1", "type": "query", "params": [], "bindings": [98], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 199}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9480822, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 25.21, "width_percent": 0.271}, {"sql": "select * from `media_files` where `media_files`.`id` = 4600 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4600], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 206}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.952106, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 25.481, "width_percent": 0.236}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 98 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 98], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 252}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.9607072, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 25.717, "width_percent": 1.355}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Xmetr\\\\Location\\\\Models\\\\City' and `slugs`.`reference_id` = 8205 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Xmetr\\Location\\Models\\City", 8205], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": "view", "name": "theme.xmetr::views.real-estate.single-layouts.partials.related-properties", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/related-properties.blade.php", "line": 23}], "start": **********.973211, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 27.072, "width_percent": 0.24}, {"sql": "select * from `states` where `states`.`id` = 197 limit 1", "type": "query", "params": [], "bindings": [197], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 313}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": "view", "name": "theme.xmetr::views.real-estate.property", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/property.blade.php", "line": 67}], "start": **********.001437, "duration": 0.03903, "duration_str": "39.03ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 27.313, "width_percent": 17.065}, {"sql": "select * from `ads`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ads/src/Supports/AdsManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\ads\\src\\Supports\\AdsManager.php", "line": 55}, {"index": 17, "namespace": null, "name": "platform/plugins/ads/src/Supports/AdsManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\ads\\src\\Supports\\AdsManager.php", "line": 46}, {"index": 18, "namespace": null, "name": "platform/plugins/ads/src/Supports/AdsManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\ads\\src\\Supports\\AdsManager.php", "line": 29}, {"index": 20, "namespace": null, "name": "platform/plugins/ads/src/Providers/AdsServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\ads\\src\\Providers\\AdsServiceProvider.php", "line": 165}], "start": **********.076399, "duration": 0.05684, "duration_str": "56.84ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 44.377, "width_percent": 24.851}, {"sql": "select * from `media_files` where `media_files`.`id` = 178 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [178], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Account.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Account.php", "line": 175}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": "view", "name": "theme.xmetr::partials.header", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/header.blade.php", "line": 62}], "start": **********.1524138, "duration": 0.028829999999999998, "duration_str": "28.83ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 69.229, "width_percent": 12.605}, {"sql": "select * from `slugs` where `reference_id` = 1229 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' limit 1", "type": "query", "params": [], "bindings": [1229, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/Listeners/AddHrefLangListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Listeners\\AddHrefLangListener.php", "line": 69}, {"index": 21, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.385558, "duration": 0.0251, "duration_str": "25.1ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 81.834, "width_percent": 10.974}, {"sql": "select * from `slugs_translations` where `slugs_translations`.`slugs_id` in (3032)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/language/src/Listeners/AddHrefLangListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Listeners\\AddHrefLangListener.php", "line": 69}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.413284, "duration": 0.0124, "duration_str": "12.4ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 92.808, "width_percent": 5.421}, {"sql": "select * from `slugs` where `reference_id` = 1229 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' limit 1", "type": "query", "params": [], "bindings": [1229, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/language/src/Listeners/AddHrefLangListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Listeners\\AddHrefLangListener.php", "line": 74}, {"index": 18, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.429775, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "AddHrefLangListener.php:74", "source": {"index": 14, "namespace": null, "name": "platform/plugins/language/src/Listeners/AddHrefLangListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Listeners\\AddHrefLangListener.php", "line": 74}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FListeners%2FAddHrefLangListener.php:74", "ajax": false, "filename": "AddHrefLangListener.php", "line": "74"}, "connection": "xmetr", "explain": null, "start_percent": 98.229, "width_percent": 0.179}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/packages/theme/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Providers\\HookServiceProvider.php", "line": 468}], "start": **********.4450672, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 98.409, "width_percent": 1.591}]}, "models": {"data": {"Xmetr\\RealEstate\\Models\\Currency": {"value": 45, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Xmetr\\Base\\Models\\MetaBox": {"value": 26, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php:1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Xmetr\\Slug\\Models\\Slug": {"value": 14, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php:1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Feature": {"value": 14, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FFeature.php:1", "ajax": false, "filename": "Feature.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Property": {"value": 5, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProperty.php:1", "ajax": false, "filename": "Property.php", "line": "?"}}, "Xmetr\\Location\\Models\\Country": {"value": 5, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCountry.php:1", "ajax": false, "filename": "Country.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Category": {"value": 5, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCategory.php:1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Account": {"value": 5, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FAccount.php:1", "ajax": false, "filename": "Account.php", "line": "?"}}, "Xmetr\\Media\\Models\\MediaFile": {"value": 4, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFile.php:1", "ajax": false, "filename": "MediaFile.php", "line": "?"}}, "Xmetr\\LanguageAdvanced\\Models\\TranslationResolver": {"value": 3, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FModels%2FTranslationResolver.php:1", "ajax": false, "filename": "TranslationResolver.php", "line": "?"}}, "Xmetr\\Location\\Models\\City": {"value": 2, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCity.php:1", "ajax": false, "filename": "City.php", "line": "?"}}, "Xmetr\\Location\\Models\\State": {"value": 2, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FState.php:1", "ajax": false, "filename": "State.php", "line": "?"}}, "Xmetr\\Page\\Models\\Page": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fpage%2Fsrc%2FModels%2FPage.php:1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\PropertyDailyView": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FPropertyDailyView.php:1", "ajax": false, "filename": "PropertyDailyView.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\SpokenLanguage": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FSpokenLanguage.php:1", "ajax": false, "filename": "SpokenLanguage.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 134, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/en/properties/long-term-rental-of-a-2-room-apartment-48m2-krakow-poland", "action_name": null, "controller_action": "Xmetr\\Theme\\Http\\Controllers\\PublicController@getViewWithPrefix", "uri": "GET en/{prefix}/{slug?}", "controller": "Xmetr\\Theme\\Http\\Controllers\\PublicController@getViewWithPrefix<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FHttp%2FControllers%2FPublicController.php:114\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/en", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FHttp%2FControllers%2FPublicController.php:114\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/packages/theme/src/Http/Controllers/PublicController.php:114-117</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect", "duration": "6.43s", "peak_memory": "66MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-807334369 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-807334369\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1837885628 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1837885628\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1703849555 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"88 characters\">https://xmetr.gc/en/properties/long-term-rental-of-a-2-room-apartment-48m2-krakow-poland</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1130 characters\">_hjSession_6417422=eyJpZCI6Ijk4M2NmMjcwLTk2OTgtNDNlYi05OGI1LTM1MmRlYmQyOTNkNCIsImMiOjE3NTU0NzExNzc1NDgsInMiOjAsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxfQ==; _ga=GA1.1.1927348753.1755471178; _hjSessionUser_6417422=eyJpZCI6IjFmM2QzZWNhLTA1MzktNTFlMS1hNmJjLTBjY2QyNDE5NDI5MCIsImNyZWF0ZWQiOjE3NTU0NzExNzc1NDYsImV4aXN0aW5nIjp0cnVlfQ==; cookie_for_consent=1; _ga_KQ6X76DET4=GS2.1.s1755471178$o1$g1$t1755472213$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IkJOT3NpRVh0UEw3eDFWczNzWHJDWFE9PSIsInZhbHVlIjoiSGVDMlhVc3VwYnBNTmtobW1mNFJETmdyUERIK3duK1FPR2FFeWkzd0JUSk85ZDNMKzZ1dzkwTHpWRnlJUlBtVkxZYlZaN2ZEZFVDSW4wTmxmNFJ0VVJKd2t6VkxDSmpqcUdreGFabG93QnkvUnZRWGZYWmJia0pQMzJKRUxkYkoiLCJtYWMiOiIyMzgzNzA5MjQ2N2YzODA2MDdiZmJmNDYwNWFhNWIxMDNkMTJiYjYyMmI1Njc2YzQ5NTNjNGM2NjZjYTMxZWQwIiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6IkNKL1pPLzNYekRDdTdkS09BZlFPUFE9PSIsInZhbHVlIjoiaERYTEdPMEJBZUxZZW1tRU55NU90WjVsbFR0Wnk1VHFvQ1JOZ0Eyb2ZCSmUya1dPckI0aTA5d2lxYWd6RjJGMVRMeUx2ZUZjd3RtRnFOdnoyRlFad29MeFVOVGU0YmRZQkl4T0VUYlZWTFpSNkVWYkk1OTZmb0FkOGZubmRVcHgiLCJtYWMiOiJiNTI3MGE0NjUwZDY0Mzg4NWMyOGVjMDkzMmYzZjU2MzhlZDk0ZDE1NmYyOTgyNDJkYmQwZjJlOTA0OGNlZTkxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703849555\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1976345349 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oR0iV9MOAFKSFDD0wTEQbJ0XvQblaV5EB8G15wwb</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nKggptuJ0kU68azRHM8gfxArXV76iA8adIeW3xds</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1976345349\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1650859170 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 17 Aug 2025 23:10:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>xmetr-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">7.4.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>developer-name</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Ishtiaq Ahmed</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>project-name</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">Xmetr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1650859170\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oR0iV9MOAFKSFDD0wTEQbJ0XvQblaV5EB8G15wwb</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">https://xmetr.gc/en/rent-properties</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>viewed_property</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>96</span> => <span class=sf-dump-num>1755454079</span>\n    <span class=sf-dump-key>747</span> => <span class=sf-dump-num>1755454176</span>\n    <span class=sf-dump-key>90</span> => <span class=sf-dump-num>1755455154</span>\n    <span class=sf-dump-key>93</span> => <span class=sf-dump-num>1755455940</span>\n    <span class=sf-dump-key>762</span> => <span class=sf-dump-num>1755456393</span>\n    <span class=sf-dump-key>1228</span> => <span class=sf-dump-num>1755456631</span>\n    <span class=sf-dump-key>1227</span> => <span class=sf-dump-num>1755456720</span>\n    <span class=sf-dump-key>1230</span> => <span class=sf-dump-num>1755459614</span>\n    <span class=sf-dump-key>1229</span> => <span class=sf-dump-num>1755469502</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property_daily</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>96</span> => <span class=sf-dump-num>1755454079</span>\n    <span class=sf-dump-key>747</span> => <span class=sf-dump-num>1755454176</span>\n    <span class=sf-dump-key>90</span> => <span class=sf-dump-num>1755455154</span>\n    <span class=sf-dump-key>93</span> => <span class=sf-dump-num>1755455940</span>\n    <span class=sf-dump-key>762</span> => <span class=sf-dump-num>1755456393</span>\n    <span class=sf-dump-key>1228</span> => <span class=sf-dump-num>1755456631</span>\n    <span class=sf-dump-key>1227</span> => <span class=sf-dump-num>1755456720</span>\n    <span class=sf-dump-key>1230</span> => <span class=sf-dump-num>1755459614</span>\n    <span class=sf-dump-key>1229</span> => <span class=sf-dump-num>1755469502</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>contact_click_property_1230_telegram</span>\" => <span class=sf-dump-num>**********</span>\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n  \"<span class=sf-dump-key>previous_language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ru</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_account_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/en/properties/long-term-rental-of-a-2-room-apartment-48m2-krakow-poland", "controller_action": "Xmetr\\Theme\\Http\\Controllers\\PublicController@getViewWithPrefix"}, "badge": null}}