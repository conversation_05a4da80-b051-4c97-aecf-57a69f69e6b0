{"__meta": {"id": "01K2X4SAF192CS8SY00XG29GYV", "datetime": "2025-08-17 23:10:43", "utime": **********.170972, "method": "GET", "uri": "/en", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1755472237.465365, "end": **********.170998, "duration": 5.705633163452148, "duration_str": "5.71s", "measures": [{"label": "Booting", "start": 1755472237.465365, "relative_start": 0, "end": **********.953243, "relative_end": **********.953243, "duration": 1.****************, "duration_str": "1.49s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.953259, "relative_start": 1.***************, "end": **********.171001, "relative_end": 2.86102294921875e-06, "duration": 4.***************, "duration_str": "4.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.022112, "relative_start": 1.****************, "end": **********.138998, "relative_end": **********.138998, "duration": 0.*****************, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: theme.xmetr::views.page", "start": **********.335476, "relative_start": 1.****************, "end": **********.335476, "relative_end": **********.335476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.shortcodes.hero-section.index", "start": **********.369199, "relative_start": 1.****************, "end": **********.369199, "relative_end": **********.369199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::layouts.full-width", "start": **********.790718, "relative_start": 2.3253531455993652, "end": **********.790718, "relative_end": **********.790718, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": **********.798104, "relative_start": 2.3327391147613525, "end": **********.798104, "relative_end": **********.798104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.header", "start": **********.799524, "relative_start": 2.3341591358184814, "end": **********.799524, "relative_end": **********.799524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": **********.81768, "relative_start": 2.3523149490356445, "end": **********.81768, "relative_end": **********.81768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": **********.821725, "relative_start": 2.3563599586486816, "end": **********.821725, "relative_end": **********.821725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": 1755472242.852213, "relative_start": 5.386847972869873, "end": 1755472242.852213, "relative_end": 1755472242.852213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": 1755472242.852844, "relative_start": 5.387479066848755, "end": 1755472242.852844, "relative_end": 1755472242.852844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": 1755472242.869839, "relative_start": 5.4044740200042725, "end": 1755472242.869839, "relative_end": 1755472242.869839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::054b349d8fa17e9f8644145c2fbc1063", "start": 1755472242.871949, "relative_start": 5.406584024429321, "end": 1755472242.871949, "relative_end": 1755472242.871949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6cb431528960518c12f7bad152d7b46d", "start": 1755472242.875254, "relative_start": 5.409888982772827, "end": 1755472242.875254, "relative_end": 1755472242.875254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": 1755472242.877808, "relative_start": 5.412443161010742, "end": 1755472242.877808, "relative_end": 1755472242.877808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f6b8d5c7f3ac3703ebfdef4449133ff4", "start": 1755472242.885466, "relative_start": 5.420101165771484, "end": 1755472242.885466, "relative_end": 1755472242.885466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f6b8d5c7f3ac3703ebfdef4449133ff4", "start": 1755472242.886187, "relative_start": 5.4208221435546875, "end": 1755472242.886187, "relative_end": 1755472242.886187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": 1755472242.886746, "relative_start": 5.421380996704102, "end": 1755472242.886746, "relative_end": 1755472242.886746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a7781df99689693edd2c6c189d767089", "start": 1755472242.888049, "relative_start": 5.422683954238892, "end": 1755472242.888049, "relative_end": 1755472242.888049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::054b349d8fa17e9f8644145c2fbc1063", "start": 1755472242.888966, "relative_start": 5.423601150512695, "end": 1755472242.888966, "relative_end": 1755472242.888966, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::054b349d8fa17e9f8644145c2fbc1063", "start": 1755472242.889513, "relative_start": 5.424148082733154, "end": 1755472242.889513, "relative_end": 1755472242.889513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6cb431528960518c12f7bad152d7b46d", "start": 1755472242.890948, "relative_start": 5.425583124160767, "end": 1755472242.890948, "relative_end": 1755472242.890948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": 1755472242.893001, "relative_start": 5.42763614654541, "end": 1755472242.893001, "relative_end": 1755472242.893001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.modal-authentication", "start": 1755472242.898063, "relative_start": 5.432698011398315, "end": 1755472242.898063, "relative_end": 1755472242.898063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": 1755472242.92068, "relative_start": 5.455315113067627, "end": 1755472242.92068, "relative_end": 1755472242.92068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form-content-only", "start": 1755472242.924738, "relative_start": 5.4593729972839355, "end": 1755472242.924738, "relative_end": 1755472242.924738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472242.925378, "relative_start": 5.460013151168823, "end": 1755472242.925378, "relative_end": 1755472242.925378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.926681, "relative_start": 5.461316108703613, "end": 1755472242.926681, "relative_end": 1755472242.926681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.927397, "relative_start": 5.462032079696655, "end": 1755472242.927397, "relative_end": 1755472242.927397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.927791, "relative_start": 5.46242618560791, "end": 1755472242.927791, "relative_end": 1755472242.927791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.92826, "relative_start": 5.462895154953003, "end": 1755472242.92826, "relative_end": 1755472242.92826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.email", "start": 1755472242.929076, "relative_start": 5.4637110233306885, "end": 1755472242.929076, "relative_end": 1755472242.929076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1755472242.930052, "relative_start": 5.46468710899353, "end": 1755472242.930052, "relative_end": 1755472242.930052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.930807, "relative_start": 5.465442180633545, "end": 1755472242.930807, "relative_end": 1755472242.930807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.931551, "relative_start": 5.466186046600342, "end": 1755472242.931551, "relative_end": 1755472242.931551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.931884, "relative_start": 5.466519117355347, "end": 1755472242.931884, "relative_end": 1755472242.931884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.932298, "relative_start": 5.466933012008667, "end": 1755472242.932298, "relative_end": 1755472242.932298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.password", "start": 1755472242.932971, "relative_start": 5.467606067657471, "end": 1755472242.932971, "relative_end": 1755472242.932971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1755472242.933766, "relative_start": 5.468400955200195, "end": 1755472242.933766, "relative_end": 1755472242.933766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.934234, "relative_start": 5.468868970870972, "end": 1755472242.934234, "relative_end": 1755472242.934234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.935012, "relative_start": 5.469647169113159, "end": 1755472242.935012, "relative_end": 1755472242.935012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.93539, "relative_start": 5.470025062561035, "end": 1755472242.93539, "relative_end": 1755472242.93539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.935847, "relative_start": 5.470482110977173, "end": 1755472242.935847, "relative_end": 1755472242.935847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472242.936328, "relative_start": 5.470963001251221, "end": 1755472242.936328, "relative_end": 1755472242.936328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.936822, "relative_start": 5.471457004547119, "end": 1755472242.936822, "relative_end": 1755472242.936822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.93749, "relative_start": 5.472125053405762, "end": 1755472242.93749, "relative_end": 1755472242.93749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.937806, "relative_start": 5.472440958023071, "end": 1755472242.937806, "relative_end": 1755472242.937806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.938175, "relative_start": 5.4728100299835205, "end": 1755472242.938175, "relative_end": 1755472242.938175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off-checkbox", "start": 1755472242.938801, "relative_start": 5.473436117172241, "end": 1755472242.938801, "relative_end": 1755472242.938801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off-checkbox", "start": 1755472242.939617, "relative_start": 5.474251985549927, "end": 1755472242.939617, "relative_end": 1755472242.939617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.on-off.checkbox", "start": 1755472242.940502, "relative_start": 5.475136995315552, "end": 1755472242.940502, "relative_end": 1755472242.940502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": 1755472242.941228, "relative_start": 5.475862979888916, "end": 1755472242.941228, "relative_end": 1755472242.941228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.942501, "relative_start": 5.477136135101318, "end": 1755472242.942501, "relative_end": 1755472242.942501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.943206, "relative_start": 5.477841138839722, "end": 1755472242.943206, "relative_end": 1755472242.943206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.943516, "relative_start": 5.478151082992554, "end": 1755472242.943516, "relative_end": 1755472242.943516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.943904, "relative_start": 5.478538990020752, "end": 1755472242.943904, "relative_end": 1755472242.943904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472242.944366, "relative_start": 5.479001045227051, "end": 1755472242.944366, "relative_end": 1755472242.944366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.944858, "relative_start": 5.479493141174316, "end": 1755472242.944858, "relative_end": 1755472242.944858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.945527, "relative_start": 5.480162143707275, "end": 1755472242.945527, "relative_end": 1755472242.945527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.945834, "relative_start": 5.480468988418579, "end": 1755472242.945834, "relative_end": 1755472242.945834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.94621, "relative_start": 5.480844974517822, "end": 1755472242.94621, "relative_end": 1755472242.94621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472242.94665, "relative_start": 5.481285095214844, "end": 1755472242.94665, "relative_end": 1755472242.94665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.947179, "relative_start": 5.48181414604187, "end": 1755472242.947179, "relative_end": 1755472242.947179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.947883, "relative_start": 5.482517957687378, "end": 1755472242.947883, "relative_end": 1755472242.947883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.948204, "relative_start": 5.482839107513428, "end": 1755472242.948204, "relative_end": 1755472242.948204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.948598, "relative_start": 5.4832329750061035, "end": 1755472242.948598, "relative_end": 1755472242.948598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472242.949048, "relative_start": 5.483683109283447, "end": 1755472242.949048, "relative_end": 1755472242.949048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.949536, "relative_start": 5.484171152114868, "end": 1755472242.949536, "relative_end": 1755472242.949536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.950212, "relative_start": 5.484847068786621, "end": 1755472242.950212, "relative_end": 1755472242.950212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.950524, "relative_start": 5.485159158706665, "end": 1755472242.950524, "relative_end": 1755472242.950524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.950914, "relative_start": 5.485548973083496, "end": 1755472242.950914, "relative_end": 1755472242.950914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472242.951359, "relative_start": 5.485994100570679, "end": 1755472242.951359, "relative_end": 1755472242.951359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.951845, "relative_start": 5.486479997634888, "end": 1755472242.951845, "relative_end": 1755472242.951845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.952523, "relative_start": 5.4871580600738525, "end": 1755472242.952523, "relative_end": 1755472242.952523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.952834, "relative_start": 5.487468957901001, "end": 1755472242.952834, "relative_end": 1755472242.952834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.953193, "relative_start": 5.487828016281128, "end": 1755472242.953193, "relative_end": 1755472242.953193, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::button", "start": 1755472242.954045, "relative_start": 5.488680124282837, "end": 1755472242.954045, "relative_end": 1755472242.954045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.954763, "relative_start": 5.489398002624512, "end": 1755472242.954763, "relative_end": 1755472242.954763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472242.955216, "relative_start": 5.489850997924805, "end": 1755472242.955216, "relative_end": 1755472242.955216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.95571, "relative_start": 5.490345001220703, "end": 1755472242.95571, "relative_end": 1755472242.95571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.956397, "relative_start": 5.491032123565674, "end": 1755472242.956397, "relative_end": 1755472242.956397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.956709, "relative_start": 5.491343975067139, "end": 1755472242.956709, "relative_end": 1755472242.956709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.957099, "relative_start": 5.491734027862549, "end": 1755472242.957099, "relative_end": 1755472242.957099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472242.957544, "relative_start": 5.4921791553497314, "end": 1755472242.957544, "relative_end": 1755472242.957544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.958029, "relative_start": 5.492664098739624, "end": 1755472242.958029, "relative_end": 1755472242.958029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.958701, "relative_start": 5.493335962295532, "end": 1755472242.958701, "relative_end": 1755472242.958701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.959012, "relative_start": 5.49364709854126, "end": 1755472242.959012, "relative_end": 1755472242.959012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.959401, "relative_start": 5.494035959243774, "end": 1755472242.959401, "relative_end": 1755472242.959401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472242.959841, "relative_start": 5.494476079940796, "end": 1755472242.959841, "relative_end": 1755472242.959841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.960329, "relative_start": 5.494964122772217, "end": 1755472242.960329, "relative_end": 1755472242.960329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.96101, "relative_start": 5.495645046234131, "end": 1755472242.96101, "relative_end": 1755472242.96101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.961734, "relative_start": 5.496369123458862, "end": 1755472242.961734, "relative_end": 1755472242.961734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.962273, "relative_start": 5.496907949447632, "end": 1755472242.962273, "relative_end": 1755472242.962273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472242.962771, "relative_start": 5.497406005859375, "end": 1755472242.962771, "relative_end": 1755472242.962771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.963288, "relative_start": 5.497923135757446, "end": 1755472242.963288, "relative_end": 1755472242.963288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.964001, "relative_start": 5.49863600730896, "end": 1755472242.964001, "relative_end": 1755472242.964001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.964443, "relative_start": 5.499078035354614, "end": 1755472242.964443, "relative_end": 1755472242.964443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.964815, "relative_start": 5.499449968338013, "end": 1755472242.964815, "relative_end": 1755472242.964815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": 1755472242.967623, "relative_start": 5.502258062362671, "end": 1755472242.967623, "relative_end": 1755472242.967623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": 1755472242.977724, "relative_start": 5.512359142303467, "end": 1755472242.977724, "relative_end": 1755472242.977724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::forms.auth", "start": 1755472242.980164, "relative_start": 5.514799118041992, "end": 1755472242.980164, "relative_end": 1755472242.980164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2844accb98b3a6a2e1cb0b4f06fc3e6e", "start": 1755472242.982003, "relative_start": 5.5166380405426025, "end": 1755472242.982003, "relative_end": 1755472242.982003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.form-open-wrapper", "start": 1755472242.982762, "relative_start": 5.517397165298462, "end": 1755472242.982762, "relative_end": 1755472242.982762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472242.98351, "relative_start": 5.5181450843811035, "end": 1755472242.98351, "relative_end": 1755472242.98351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.984088, "relative_start": 5.518723011016846, "end": 1755472242.984088, "relative_end": 1755472242.984088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.985127, "relative_start": 5.51976203918457, "end": 1755472242.985127, "relative_end": 1755472242.985127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.985494, "relative_start": 5.520128965377808, "end": 1755472242.985494, "relative_end": 1755472242.985494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.985901, "relative_start": 5.520536184310913, "end": 1755472242.985901, "relative_end": 1755472242.985901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1755472242.986599, "relative_start": 5.521234035491943, "end": 1755472242.986599, "relative_end": 1755472242.986599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1755472242.987459, "relative_start": 5.522094011306763, "end": 1755472242.987459, "relative_end": 1755472242.987459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.987962, "relative_start": 5.522597074508667, "end": 1755472242.987962, "relative_end": 1755472242.987962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.98872, "relative_start": 5.523355007171631, "end": 1755472242.98872, "relative_end": 1755472242.98872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.989062, "relative_start": 5.523697137832642, "end": 1755472242.989062, "relative_end": 1755472242.989062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.989501, "relative_start": 5.524136066436768, "end": 1755472242.989501, "relative_end": 1755472242.989501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.email", "start": 1755472242.9902, "relative_start": 5.524835109710693, "end": 1755472242.9902, "relative_end": 1755472242.9902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1755472242.990808, "relative_start": 5.525443077087402, "end": 1755472242.990808, "relative_end": 1755472242.990808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.991315, "relative_start": 5.525949954986572, "end": 1755472242.991315, "relative_end": 1755472242.991315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.99218, "relative_start": 5.526815176010132, "end": 1755472242.99218, "relative_end": 1755472242.99218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.992577, "relative_start": 5.527212142944336, "end": 1755472242.992577, "relative_end": 1755472242.992577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.993129, "relative_start": 5.527764081954956, "end": 1755472242.993129, "relative_end": 1755472242.993129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.password", "start": 1755472242.99377, "relative_start": 5.528404951095581, "end": 1755472242.99377, "relative_end": 1755472242.99377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1755472242.99433, "relative_start": 5.528964996337891, "end": 1755472242.99433, "relative_end": 1755472242.99433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.994678, "relative_start": 5.529313087463379, "end": 1755472242.994678, "relative_end": 1755472242.994678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.995647, "relative_start": 5.530282020568848, "end": 1755472242.995647, "relative_end": 1755472242.995647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.995989, "relative_start": 5.530624151229858, "end": 1755472242.995989, "relative_end": 1755472242.995989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.996439, "relative_start": 5.531074047088623, "end": 1755472242.996439, "relative_end": 1755472242.996439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472242.996968, "relative_start": 5.531603097915649, "end": 1755472242.996968, "relative_end": 1755472242.996968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1755472242.99755, "relative_start": 5.532185077667236, "end": 1755472242.99755, "relative_end": 1755472242.99755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1755472242.998456, "relative_start": 5.533091068267822, "end": 1755472242.998456, "relative_end": 1755472242.998456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1755472242.998862, "relative_start": 5.533497095108032, "end": 1755472242.998862, "relative_end": 1755472242.998862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1755472242.999353, "relative_start": 5.533987998962402, "end": 1755472242.999353, "relative_end": 1755472242.999353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1755472242.999925, "relative_start": 5.534559965133667, "end": 1755472242.999925, "relative_end": 1755472242.999925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.000579, "relative_start": 5.535214185714722, "end": **********.000579, "relative_end": **********.000579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.001375, "relative_start": 5.536010026931763, "end": **********.001375, "relative_end": **********.001375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.001794, "relative_start": 5.536429166793823, "end": **********.001794, "relative_end": **********.001794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.002246, "relative_start": 5.536880970001221, "end": **********.002246, "relative_end": **********.002246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::button", "start": **********.003002, "relative_start": 5.537636995315552, "end": **********.003002, "relative_end": **********.003002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.003586, "relative_start": 5.538221120834351, "end": **********.003586, "relative_end": **********.003586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.004105, "relative_start": 5.538740158081055, "end": **********.004105, "relative_end": **********.004105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.004666, "relative_start": 5.539301156997681, "end": **********.004666, "relative_end": **********.004666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.005417, "relative_start": 5.540052175521851, "end": **********.005417, "relative_end": **********.005417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.005753, "relative_start": 5.540388107299805, "end": **********.005753, "relative_end": **********.005753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.00617, "relative_start": 5.540805101394653, "end": **********.00617, "relative_end": **********.00617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.006645, "relative_start": 5.541280031204224, "end": **********.006645, "relative_end": **********.006645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.007162, "relative_start": 5.541797161102295, "end": **********.007162, "relative_end": **********.007162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.007879, "relative_start": 5.542514085769653, "end": **********.007879, "relative_end": **********.007879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.008211, "relative_start": 5.542845964431763, "end": **********.008211, "relative_end": **********.008211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.008658, "relative_start": 5.543292999267578, "end": **********.008658, "relative_end": **********.008658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.009223, "relative_start": 5.543858051300049, "end": **********.009223, "relative_end": **********.009223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.009675, "relative_start": 5.544310092926025, "end": **********.009675, "relative_end": **********.009675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.010315, "relative_start": 5.544950008392334, "end": **********.010315, "relative_end": **********.010315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.010565, "relative_start": 5.5452001094818115, "end": **********.010565, "relative_end": **********.010565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.010974, "relative_start": 5.545608997344971, "end": **********.010974, "relative_end": **********.010974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.011493, "relative_start": 5.546128034591675, "end": **********.011493, "relative_end": **********.011493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.012146, "relative_start": 5.546781063079834, "end": **********.012146, "relative_end": **********.012146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.012977, "relative_start": 5.547611951828003, "end": **********.012977, "relative_end": **********.012977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.013267, "relative_start": 5.5479021072387695, "end": **********.013267, "relative_end": **********.013267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.013564, "relative_start": 5.54819917678833, "end": **********.013564, "relative_end": **********.013564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.016414, "relative_start": 5.551048994064331, "end": **********.016414, "relative_end": **********.016414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.footer", "start": **********.018754, "relative_start": 5.553389072418213, "end": **********.018754, "relative_end": **********.018754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": **********.020551, "relative_start": 5.555186033248901, "end": **********.020551, "relative_end": **********.020551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::layouts.base", "start": **********.021413, "relative_start": 5.556048154830933, "end": **********.021413, "relative_end": **********.021413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.header", "start": **********.028158, "relative_start": 5.562793016433716, "end": **********.028158, "relative_end": **********.028158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.footer", "start": **********.151971, "relative_start": 5.686606168746948, "end": **********.151971, "relative_end": **********.151971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::fronts.toast-notification", "start": **********.154958, "relative_start": 5.689593076705933, "end": **********.154958, "relative_end": **********.154958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.166267, "relative_start": 5.700901985168457, "end": **********.166571, "relative_end": **********.166571, "duration": 0.0003039836883544922, "duration_str": "304μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 63439424, "peak_usage_str": "61MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 163, "nb_templates": 163, "templates": [{"name": "1x theme.xmetr::views.page", "param_count": null, "params": [], "start": **********.335426, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/page.blade.phptheme.xmetr::views.page", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Fpage.blade.php:1", "ajax": false, "filename": "page.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.page"}, {"name": "1x theme.xmetr::partials.shortcodes.hero-section.index", "param_count": null, "params": [], "start": **********.369155, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/shortcodes/hero-section/index.blade.phptheme.xmetr::partials.shortcodes.hero-section.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Fshortcodes%2Fhero-section%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.shortcodes.hero-section.index"}, {"name": "1x theme.xmetr::layouts.full-width", "param_count": null, "params": [], "start": **********.79067, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/layouts/full-width.blade.phptheme.xmetr::layouts.full-width", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Flayouts%2Ffull-width.blade.php:1", "ajax": false, "filename": "full-width.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::layouts.full-width"}, {"name": "4x plugins/ads::partials.ad-display", "param_count": null, "params": [], "start": **********.798059, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/ads/resources/views/partials/ad-display.blade.phpplugins/ads::partials.ad-display", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Fads%2Fresources%2Fviews%2Fpartials%2Fad-display.blade.php:1", "ajax": false, "filename": "ad-display.blade.php", "line": "?"}, "render_count": 4, "name_original": "plugins/ads::partials.ad-display"}, {"name": "1x theme.xmetr::partials.header", "param_count": null, "params": [], "start": **********.799485, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/header.blade.phptheme.xmetr::partials.header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.header"}, {"name": "4x plugins/social-login::login-options", "param_count": null, "params": [], "start": **********.817631, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/social-login/resources/views/login-options.blade.phpplugins/social-login::login-options", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Fsocial-login%2Fresources%2Fviews%2Flogin-options.blade.php:1", "ajax": false, "filename": "login-options.blade.php", "line": "?"}, "render_count": 4, "name_original": "plugins/social-login::login-options"}, {"name": "2x __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "param_count": null, "params": [], "start": 1755472242.869791, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php__components::98e88d58787b8dfeb6f0d1dc0a785cfd", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php:1", "ajax": false, "filename": "98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::98e88d58787b8dfeb6f0d1dc0a785cfd"}, {"name": "3x __components::054b349d8fa17e9f8644145c2fbc1063", "param_count": null, "params": [], "start": 1755472242.871909, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/054b349d8fa17e9f8644145c2fbc1063.blade.php__components::054b349d8fa17e9f8644145c2fbc1063", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F054b349d8fa17e9f8644145c2fbc1063.blade.php:1", "ajax": false, "filename": "054b349d8fa17e9f8644145c2fbc1063.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::054b349d8fa17e9f8644145c2fbc1063"}, {"name": "2x __components::6cb431528960518c12f7bad152d7b46d", "param_count": null, "params": [], "start": 1755472242.875216, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/6cb431528960518c12f7bad152d7b46d.blade.php__components::6cb431528960518c12f7bad152d7b46d", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F6cb431528960518c12f7bad152d7b46d.blade.php:1", "ajax": false, "filename": "6cb431528960518c12f7bad152d7b46d.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::6cb431528960518c12f7bad152d7b46d"}, {"name": "2x __components::f6b8d5c7f3ac3703ebfdef4449133ff4", "param_count": null, "params": [], "start": 1755472242.885427, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/f6b8d5c7f3ac3703ebfdef4449133ff4.blade.php__components::f6b8d5c7f3ac3703ebfdef4449133ff4", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ff6b8d5c7f3ac3703ebfdef4449133ff4.blade.php:1", "ajax": false, "filename": "f6b8d5c7f3ac3703ebfdef4449133ff4.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::f6b8d5c7f3ac3703ebfdef4449133ff4"}, {"name": "1x __components::a7781df99689693edd2c6c189d767089", "param_count": null, "params": [], "start": 1755472242.888011, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/a7781df99689693edd2c6c189d767089.blade.php__components::a7781df99689693edd2c6c189d767089", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fa7781df99689693edd2c6c189d767089.blade.php:1", "ajax": false, "filename": "a7781df99689693edd2c6c189d767089.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a7781df99689693edd2c6c189d767089"}, {"name": "1x theme.xmetr::partials.modal-authentication", "param_count": null, "params": [], "start": 1755472242.898018, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/modal-authentication.blade.phptheme.xmetr::partials.modal-authentication", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Fmodal-authentication.blade.php:1", "ajax": false, "filename": "modal-authentication.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.modal-authentication"}, {"name": "4x core/js-validation::bootstrap", "param_count": null, "params": [], "start": 1755472242.920655, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php:1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/js-validation::bootstrap"}, {"name": "1x core/base::forms.form-content-only", "param_count": null, "params": [], "start": 1755472242.924713, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form-content-only.blade.phpcore/base::forms.form-content-only", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform-content-only.blade.php:1", "ajax": false, "filename": "form-content-only.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form-content-only"}, {"name": "17x core/base::forms.fields.html", "param_count": null, "params": [], "start": 1755472242.925356, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/html.blade.phpcore/base::forms.fields.html", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fhtml.blade.php:1", "ajax": false, "filename": "html.blade.php", "line": "?"}, "render_count": 17, "name_original": "core/base::forms.fields.html"}, {"name": "23x ********************************::form.field", "param_count": null, "params": [], "start": 1755472242.926659, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/field.blade.php********************************::form.field", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php:1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 23, "name_original": "********************************::form.field"}, {"name": "23x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": 1755472242.927374, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php:1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 23, "name_original": "core/base::forms.partials.help-block"}, {"name": "23x core/base::forms.partials.errors", "param_count": null, "params": [], "start": 1755472242.927768, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php:1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 23, "name_original": "core/base::forms.partials.errors"}, {"name": "25x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": 1755472242.928238, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php:1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 25, "name_original": "core/base::forms.columns.column-span"}, {"name": "2x core/base::forms.fields.email", "param_count": null, "params": [], "start": 1755472242.928724, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/email.blade.phpcore/base::forms.fields.email", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Femail.blade.php:1", "ajax": false, "filename": "email.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.email"}, {"name": "5x core/base::forms.partials.label", "param_count": null, "params": [], "start": 1755472242.930012, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/label.blade.phpcore/base::forms.partials.label", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Flabel.blade.php:1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 5, "name_original": "core/base::forms.partials.label"}, {"name": "2x core/base::forms.fields.password", "param_count": null, "params": [], "start": 1755472242.93293, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/password.blade.phpcore/base::forms.fields.password", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fpassword.blade.php:1", "ajax": false, "filename": "password.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.password"}, {"name": "1x core/base::forms.fields.on-off-checkbox", "param_count": null, "params": [], "start": 1755472242.938764, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/on-off-checkbox.blade.phpcore/base::forms.fields.on-off-checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fon-off-checkbox.blade.php:1", "ajax": false, "filename": "on-off-checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.on-off-checkbox"}, {"name": "1x core/base::forms.partials.on-off-checkbox", "param_count": null, "params": [], "start": 1755472242.939578, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/on-off-checkbox.blade.phpcore/base::forms.partials.on-off-checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fon-off-checkbox.blade.php:1", "ajax": false, "filename": "on-off-checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.on-off-checkbox"}, {"name": "1x ********************************::form.on-off.checkbox", "param_count": null, "params": [], "start": 1755472242.940464, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/on-off/checkbox.blade.php********************************::form.on-off.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fon-off%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.on-off.checkbox"}, {"name": "1x core/base::components.form.checkbox", "param_count": null, "params": [], "start": 1755472242.941189, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.phpcore/base::components.form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.form.checkbox"}, {"name": "2x laravel-form-builder::button", "param_count": null, "params": [], "start": 1755472242.954007, "type": "php", "hash": "phpD:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src/../resources/views/button.phplaravel-form-builder::button", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fform-builder%2Fresources%2Fviews%2Fbutton.php:1", "ajax": false, "filename": "button.php", "line": "?"}, "render_count": 2, "name_original": "laravel-form-builder::button"}, {"name": "1x plugins/real-estate::forms.auth", "param_count": null, "params": [], "start": 1755472242.980122, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/forms/auth.blade.phpplugins/real-estate::forms.auth", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fforms%2Fauth.blade.php:1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::forms.auth"}, {"name": "1x __components::2844accb98b3a6a2e1cb0b4f06fc3e6e", "param_count": null, "params": [], "start": 1755472242.981963, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/2844accb98b3a6a2e1cb0b4f06fc3e6e.blade.php__components::2844accb98b3a6a2e1cb0b4f06fc3e6e", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F2844accb98b3a6a2e1cb0b4f06fc3e6e.blade.php:1", "ajax": false, "filename": "2844accb98b3a6a2e1cb0b4f06fc3e6e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2844accb98b3a6a2e1cb0b4f06fc3e6e"}, {"name": "1x core/base::forms.columns.form-open-wrapper", "param_count": null, "params": [], "start": 1755472242.982719, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/columns/form-open-wrapper.blade.phpcore/base::forms.columns.form-open-wrapper", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fform-open-wrapper.blade.php:1", "ajax": false, "filename": "form-open-wrapper.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.columns.form-open-wrapper"}, {"name": "1x core/base::forms.fields.text", "param_count": null, "params": [], "start": 1755472242.986558, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/text.blade.phpcore/base::forms.fields.text", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftext.blade.php:1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.text"}, {"name": "1x theme.xmetr::partials.footer", "param_count": null, "params": [], "start": **********.018717, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/footer.blade.phptheme.xmetr::partials.footer", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Ffooter.blade.php:1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.footer"}, {"name": "1x theme.xmetr::layouts.base", "param_count": null, "params": [], "start": **********.021374, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/layouts/base.blade.phptheme.xmetr::layouts.base", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Flayouts%2Fbase.blade.php:1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::layouts.base"}, {"name": "1x packages/theme::partials.header", "param_count": null, "params": [], "start": **********.028112, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/theme/resources/views/partials/header.blade.phppackages/theme::partials.header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.header"}, {"name": "1x packages/theme::partials.footer", "param_count": null, "params": [], "start": **********.151915, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/theme/resources/views/partials/footer.blade.phppackages/theme::partials.footer", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Ffooter.blade.php:1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.footer"}, {"name": "1x packages/theme::fronts.toast-notification", "param_count": null, "params": [], "start": **********.154907, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/theme/resources/views/fronts/toast-notification.blade.phppackages/theme::fronts.toast-notification", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Ffronts%2Ftoast-notification.blade.php:1", "ajax": false, "filename": "toast-notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::fronts.toast-notification"}]}, "queries": {"count": 12, "nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.14418, "accumulated_duration_str": "144ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/packages/page/src/Services/PageService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Services\\PageService.php", "line": 30}], "start": **********.202262, "duration": 0.01447, "duration_str": "14.47ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 10.036}, {"sql": "select * from `pages` where (`id` = '1' and `status` = 'published') limit 1", "type": "query", "params": [], "bindings": ["1", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/page/src/Services/PageService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Services\\PageService.php", "line": 38}, {"index": 18, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 27}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.224834, "duration": 0.03033, "duration_str": "30.33ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 10.036, "width_percent": 21.036}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (1) and `slugs`.`reference_type` = 'Xmetr\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Xmetr\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/page/src/Services/PageService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Services\\PageService.php", "line": 38}, {"index": 24, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.262933, "duration": 0.04047, "duration_str": "40.47ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 31.072, "width_percent": 28.069}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\Page\\\\Models\\\\Page' and `meta_boxes`.`reference_id` = 1 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\Page\\Models\\Page", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.page", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/page.blade.php", "line": 2}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.337589, "duration": 0.01446, "duration_str": "14.46ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 59.141, "width_percent": 10.029}, {"sql": "select `id`, `name` from `pages` where `status` = 'published' and `id` = '14' limit 1", "type": "query", "params": [], "bindings": ["published", "14"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 168}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 183}, {"index": 20, "namespace": "view", "name": "theme.xmetr::partials.shortcodes.hero-section.index", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/shortcodes/hero-section/index.blade.php", "line": 27}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.6171129, "duration": 0.0185, "duration_str": "18.5ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 69.17, "width_percent": 12.831}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (14) and `slugs`.`reference_type` = 'Xmetr\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Xmetr\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 168}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/RealEstateHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\RealEstateHelper.php", "line": 183}, {"index": 26, "namespace": "view", "name": "theme.xmetr::partials.shortcodes.hero-section.index", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/shortcodes/hero-section/index.blade.php", "line": 27}], "start": **********.6378639, "duration": 0.02042, "duration_str": "20.42ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 82.002, "width_percent": 14.163}, {"sql": "select * from `countries` where exists (select * from `re_properties` where `countries`.`id` = `re_properties`.`country_id`) order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 141}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.667532, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 96.165, "width_percent": 0.375}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (22, 24, 27, 29, 30, 31, 32, 33, 34, 35, 37, 44, 49, 52, 110, 115, 149, 199) and `slugs`.`reference_type` = 'Xmetr\\\\Location\\\\Models\\\\Country'", "type": "query", "params": [], "bindings": ["Xmetr\\Location\\Models\\Country"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 141}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6741102, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 96.539, "width_percent": 0.444}, {"sql": "select * from `re_categories` where (`status` = 'published') order by `created_at` asc, `is_default` asc, `order` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/CategoryRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\CategoryRepository.php", "line": 21}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/helpers/helpers.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\helpers.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.770309, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 96.983, "width_percent": 1.699}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (4, 7, 8) and `slugs`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Category'", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Category"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/CategoryRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\CategoryRepository.php", "line": 21}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/helpers/helpers.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\helpers.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.775871, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 98.682, "width_percent": 0.347}, {"sql": "select * from `ads`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ads/src/Supports/AdsManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\ads\\src\\Supports\\AdsManager.php", "line": 55}, {"index": 17, "namespace": null, "name": "platform/plugins/ads/src/Supports/AdsManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\ads\\src\\Supports\\AdsManager.php", "line": 46}, {"index": 18, "namespace": null, "name": "platform/plugins/ads/src/Supports/AdsManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\ads\\src\\Supports\\AdsManager.php", "line": 29}, {"index": 20, "namespace": null, "name": "platform/plugins/ads/src/Providers/AdsServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\ads\\src\\Providers\\AdsServiceProvider.php", "line": 165}], "start": **********.794243, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 99.029, "width_percent": 0.458}, {"sql": "select * from `re_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/helpers/currencies.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\currencies.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.826361, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 99.487, "width_percent": 0.513}]}, "models": {"data": {"Xmetr\\RealEstate\\Models\\Currency": {"value": 43, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Xmetr\\Slug\\Models\\Slug": {"value": 25, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php:1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Xmetr\\Location\\Models\\Country": {"value": 18, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCountry.php:1", "ajax": false, "filename": "Country.php", "line": "?"}}, "Xmetr\\Base\\Models\\MetaBox": {"value": 3, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php:1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Category": {"value": 3, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCategory.php:1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Xmetr\\Page\\Models\\Page": {"value": 2, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fpage%2Fsrc%2FModels%2FPage.php:1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 95, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/en", "action_name": "public.index", "controller_action": "Xmetr\\Theme\\Http\\Controllers\\PublicController@getIndex", "uri": "GET en", "controller": "Xmetr\\Theme\\Http\\Controllers\\PublicController@getIndex<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FHttp%2FControllers%2FPublicController.php:22\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/en", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FHttp%2FControllers%2FPublicController.php:22\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/packages/theme/src/Http/Controllers/PublicController.php:22-39</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect", "duration": "5.71s", "peak_memory": "64MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1016242064 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1016242064\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1504568863 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1504568863\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2032964752 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">https://xmetr.gc/en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1130 characters\">_hjSession_6417422=eyJpZCI6Ijk4M2NmMjcwLTk2OTgtNDNlYi05OGI1LTM1MmRlYmQyOTNkNCIsImMiOjE3NTU0NzExNzc1NDgsInMiOjAsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxfQ==; _ga=GA1.1.1927348753.1755471178; _hjSessionUser_6417422=eyJpZCI6IjFmM2QzZWNhLTA1MzktNTFlMS1hNmJjLTBjY2QyNDE5NDI5MCIsImNyZWF0ZWQiOjE3NTU0NzExNzc1NDYsImV4aXN0aW5nIjp0cnVlfQ==; cookie_for_consent=1; _ga_KQ6X76DET4=GS2.1.s1755471178$o1$g1$t1755472229$j44$l0$h0; XSRF-TOKEN=eyJpdiI6Im4vVTZZWmdIR0Z4UTdaUnJJTWl1WWc9PSIsInZhbHVlIjoic1pNV0ozYTd5eWl2ckJwSXpQa1JYb3Q2V1pvcnJEU0ZWSW82VW56UmRMc3NqWTdpVHg1UTNFQ1VKalVzVVFaZFc4WXRZbWRaRzgvUFB3VVhZdGVUdjlkVmk5SCtuS2FsRTB3UnN1cnl6NEt0NjlOanJ0eVFUa3NxN043ODhXTmEiLCJtYWMiOiJjZjZhYWQzMDEyMzgzMzgwNTQ5Y2VjOTM1ZDgwZTVkMGMxNzJlYWYzN2YyNjg5OTU1MjYwMjFlZTg4ZWUyYTA3IiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6InJHMHlYOC90WUN2NTExeHY2dWFrWVE9PSIsInZhbHVlIjoiWUw2cmNIMGxQY0R1RXBXVWxURlpxMy90U0Z6OXZTVU0waFZ4Z2M2Qm9zZHJHVVl3QytGRDE2aTFXVXBrRkZ5SmtxT1BGbGxacW9BZE9jcHpuSml4eTU1cjB0T0hoWkh2bWRQeWw4bDdRSGcxWVNBSHdETGR3Ti9kcnZIQ2l5WmIiLCJtYWMiOiIyYmVkZjBlZTRjZDg4MzcyMWQyMmVlMDc5Nzc5MTQwYzRjZDI5MTI1MDczZGU3Njg2OGIwZTYwZTdhOWY5MTcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2032964752\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-194241222 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SpavgfhqPsU5JT0rwVwub2px7qWmwj0ouWvQBFmZ</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uvBdtHpHElv7tkBreJNh86xe9kTYweWfvpeiseVe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-194241222\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1516776205 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 17 Aug 2025 23:10:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>xmetr-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">7.4.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>developer-name</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Ishtiaq Ahmed</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>project-name</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">Xmetr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1516776205\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1678492532 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SpavgfhqPsU5JT0rwVwub2px7qWmwj0ouWvQBFmZ</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">https://xmetr.gc/en/rent-properties</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>viewed_property</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>96</span> => <span class=sf-dump-num>1755454079</span>\n    <span class=sf-dump-key>747</span> => <span class=sf-dump-num>1755454176</span>\n    <span class=sf-dump-key>90</span> => <span class=sf-dump-num>1755455154</span>\n    <span class=sf-dump-key>93</span> => <span class=sf-dump-num>1755455940</span>\n    <span class=sf-dump-key>762</span> => <span class=sf-dump-num>1755456393</span>\n    <span class=sf-dump-key>1228</span> => <span class=sf-dump-num>1755456631</span>\n    <span class=sf-dump-key>1227</span> => <span class=sf-dump-num>1755456720</span>\n    <span class=sf-dump-key>1230</span> => <span class=sf-dump-num>1755459614</span>\n    <span class=sf-dump-key>1229</span> => <span class=sf-dump-num>1755469502</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property_daily</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>96</span> => <span class=sf-dump-num>1755454079</span>\n    <span class=sf-dump-key>747</span> => <span class=sf-dump-num>1755454176</span>\n    <span class=sf-dump-key>90</span> => <span class=sf-dump-num>1755455154</span>\n    <span class=sf-dump-key>93</span> => <span class=sf-dump-num>1755455940</span>\n    <span class=sf-dump-key>762</span> => <span class=sf-dump-num>1755456393</span>\n    <span class=sf-dump-key>1228</span> => <span class=sf-dump-num>1755456631</span>\n    <span class=sf-dump-key>1227</span> => <span class=sf-dump-num>1755456720</span>\n    <span class=sf-dump-key>1230</span> => <span class=sf-dump-num>1755459614</span>\n    <span class=sf-dump-key>1229</span> => <span class=sf-dump-num>1755469502</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>contact_click_property_1230_telegram</span>\" => <span class=sf-dump-num>1755459691</span>\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n  \"<span class=sf-dump-key>previous_language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ru</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1678492532\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/en", "action_name": "public.index", "controller_action": "Xmetr\\Theme\\Http\\Controllers\\PublicController@getIndex"}, "badge": null}}