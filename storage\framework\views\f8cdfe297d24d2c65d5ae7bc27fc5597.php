<!-- Set global authentication status -->
<script>
    window.isLoggedIn = <?php echo e(auth('account')->check() ? 'true' : 'false'); ?>;
</script>

<!-- Main Header Nav -->
<header class="nav-homepage-style header-nav stricky main-menu shadow-none bg-white !border-b !border-b-[#DDDDDD] !py-[17px] z-[20]">
    <div class="container posr menu_bdrt1">
      <div class="flex flex-row flex-nowrap align-items-center justify-content-between">
        <div class="col-auto pe-0">
          <div class="d-flex align-items-center justify-content-between">
            <div class="logos mr10 d-flex justify-content-center align-items-center">
                <a class="header-logo logo1" href="<?php echo e(BaseHelper::getHomepageUrl()); ?>">
                    <?php echo e(Theme::getLogoImage(maxHeight: 46)); ?>

                </a>
                <a class="header-logo logo2" href="<?php echo e(BaseHelper::getHomepageUrl()); ?>">
                    <?php echo e(Theme::getLogoImage(maxHeight: 46)); ?>

                </a>
              
              
            </div>
            <!-- Responsive Menu Structure-->
            <?php echo Menu::renderMenuLocation('main-menu', [
                'options' => ['class' => 'ace-responsive-menu', 'data-menu-style'=> 'horizontal'],
                'view' => 'main-menu',
            ]); ?>

          </div>
        </div>
        <div class="col-auto">
          <div class="text-center text-lg-end header_right_widgets">
          </div>

          <div class="d-flex align-items-center gap-[20px]">
            <!-- Open select lannguage modal -->
            <a href="#modalSelectLang" role="button" data-bs-toggle="modal">
              <svg width="23" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M10 20C4.477 20 0 15.523 0 10C0 4.477 4.477 0 10 0C15.523 0 20 4.477 20 10C20 15.523 15.523 20 10 20ZM7.71 17.667C6.72341 15.5743 6.15187 13.3102 6.027 11H2.062C2.25659 12.5389 2.89392 13.9882 3.89657 15.1717C4.89922 16.3552 6.22401 17.2221 7.71 17.667ZM8.03 11C8.181 13.439 8.878 15.73 10 17.752C11.1523 15.6766 11.8254 13.3695 11.97 11H8.03ZM17.938 11H13.973C13.8481 13.3102 13.2766 15.5743 12.29 17.667C13.776 17.2221 15.1008 16.3552 16.1034 15.1717C17.1061 13.9882 17.7434 12.5389 17.938 11ZM2.062 9H6.027C6.15187 6.68979 6.72341 4.42569 7.71 2.333C6.22401 2.77788 4.89922 3.64475 3.89657 4.8283C2.89392 6.01184 2.25659 7.4611 2.062 9ZM8.031 9H11.969C11.8248 6.6306 11.152 4.32353 10 2.248C8.84768 4.32345 8.17456 6.63052 8.03 9H8.031ZM12.29 2.333C13.2766 4.42569 13.8481 6.68979 13.973 9H17.938C17.7434 7.4611 17.1061 6.01184 16.1034 4.8283C15.1008 3.64475 13.776 2.77788 12.29 2.333Z" fill="black" />
              </svg>
            </a>

            <?php if(RealEstateHelper::isEnabledWishlist()): ?>

                <a href="<?php echo e(route('public.wishlist')); ?>" role="button" class="position-relative">
                    <svg width="23" height="20" viewBox="0 0 23 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M12.4338 18.0866C11.6493 18.8948 10.3524 18.896 9.56633 18.0893L6.82495 15.2757L2.68131 10.9892C0.439563 8.62015 0.439563 4.9123 2.68131 2.54328C3.779 1.48831 5.26577 0.937006 6.7859 1.02128C8.30608 1.10555 9.72278 1.81781 10.6971 2.98765L11.0014 3.28628L11.3029 2.97479C12.2773 1.80495 13.694 1.09269 15.2141 1.00842C16.7342 0.924146 18.2211 1.47545 19.3187 2.53042C21.5604 4.89944 21.5604 8.60729 19.3187 10.9763L15.1751 15.2628L12.4338 18.0866Z" stroke="#212329" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    
                  </a>
            <?php endif; ?>



            <?php if(auth('account')->check()): ?>

              <!-- * USER START Logined -->
            <!-- Dropdown -->

            <div class="dropdown">
              <a class="login-info d-flex align-items-center" href="#" data-bs-toggle="dropdown" role="button"><img src="<?php echo e(auth('account')->user()->avatar_url); ?>" alt="<?php echo e(auth('account')->user()->name); ?>" style="border-radius: 50%; width:45px; height: 45px;"></a>
              <div class="dropdown-menu h-fit w-fit border-none duration-200 p-0">
                <div class="w-[250px] bg-white border border-[#D6D6D7] rounded-[10px] flex flex-col gap-[20px] px-[20px] py-[25px]">
                  <a class="text-black text-[15px] font-bold" href="<?php echo e(route('public.account.settings')); ?>" role="button"><?php echo e(__('Profile')); ?></a>
                  <a class="text-black text-[15px] font-bold" href="<?php echo e(route('public.wishlist')); ?>" role="button"><?php echo e(__('My favorites')); ?></a>
                  <a class="text-black text-[15px] font-bold" href="<?php echo e(route('public.account.properties.index')); ?>"><?php echo e(__('My listings')); ?></a>
                  <a class="text-black text-[15px] font-bold " href="<?php echo e(route('public.account.logout')); ?>" onclick="event.preventDefault(); localStorage.setItem('xmetr_logout_success', Date.now().toString()); document.getElementById('logout-form').submit();"><?php echo e(__('Log out')); ?></a>
                  <form id="logout-form" style="display: none;" action="<?php echo e(route('public.account.logout')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="redirect_url" value="<?php echo e(url()->current()); ?>">
                </form>
                </div>
              </div>
            </div>

            <?php else: ?>




            <!-- Signup Signin/ Sign Up -->
            <div class="dropdown">
              <a class="login-info d-flex align-items-center" href="#" data-bs-toggle="dropdown" role="button"><img src="<?php echo e(Theme::asset()->url('images/resource/user-icon.png')); ?>" alt="user.png" style="border-radius: 50%; width:45px; height: 45px;"></a>
              <div class="dropdown-menu h-fit w-fit border-none duration-200 p-0">
                <div class="w-[250px] bg-white border border-[#D6D6D7] rounded-[10px] flex flex-col gap-[20px] px-[20px] py-[25px]">
                  <a class="text-black text-[15px] font-bold capitalize" href="#modalSignin" role="button" data-bs-toggle="modal"><?php echo e(__('Sign in')); ?></a>
                  <a class="text-black text-[15px] font-bold capitalize" href="#modalSignup" role="button" data-bs-toggle="modal"><?php echo e(__('Sign up')); ?></a>
                </div>
              </div>
            </div>

            <?php endif; ?>

            <!-- * USER END -->

            <!-- Add new property -->
            <div class="relative">
              <div class="free-badge absolute top-0 -right-[35px] w-fit lowercase"><?php echo e(__('Free')); ?></div>
              <a href="<?php echo e(route('public.account.properties.create')); ?>" class="ud-btn btn-thm add-property bdrs12 border-0 relative leading-7 rounded-[100px]" role="button">
                <?php echo e(__('Add listing')); ?>

              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <!-- Header placeholder. Height must be equal to header height -->
  <div class="invisible h-[88px] max-[1024px]:h-[71px]"></div>


  <!-- Mobile Nav -->
  <div class="mobile-menu">
    <div class="header innerpage-style !h-fit !py-[10px] shadow-none border-b border-[#DDDDDD]" style="border-style: solid;">
      <div class="menu_and_widgets flex justify-between items-center gap-[16px]">
        <a class="mobile_logo" href="<?php echo e(url('/')); ?>">
          <img src="<?php echo e(Theme::asset()->url('images/header-logo2.png')); ?>" alt="" width="100px" height="46px">
        </a>

        <div class="flex items-center gap-[20px]">


            <!-- Open select lannguage modal -->
            <a href="#modalSelectLang" role="button" data-bs-toggle="modal">
              <svg width="23" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M10 20C4.477 20 0 15.523 0 10C0 4.477 4.477 0 10 0C15.523 0 20 4.477 20 10C20 15.523 15.523 20 10 20ZM7.71 17.667C6.72341 15.5743 6.15187 13.3102 6.027 11H2.062C2.25659 12.5389 2.89392 13.9882 3.89657 15.1717C4.89922 16.3552 6.22401 17.2221 7.71 17.667ZM8.03 11C8.181 13.439 8.878 15.73 10 17.752C11.1523 15.6766 11.8254 13.3695 11.97 11H8.03ZM17.938 11H13.973C13.8481 13.3102 13.2766 15.5743 12.29 17.667C13.776 17.2221 15.1008 16.3552 16.1034 15.1717C17.1061 13.9882 17.7434 12.5389 17.938 11ZM2.062 9H6.027C6.15187 6.68979 6.72341 4.42569 7.71 2.333C6.22401 2.77788 4.89922 3.64475 3.89657 4.8283C2.89392 6.01184 2.25659 7.4611 2.062 9ZM8.031 9H11.969C11.8248 6.6306 11.152 4.32353 10 2.248C8.84768 4.32345 8.17456 6.63052 8.03 9H8.031ZM12.29 2.333C13.2766 4.42569 13.8481 6.68979 13.973 9H17.938C17.7434 7.4611 17.1061 6.01184 16.1034 4.8283C15.1008 3.64475 13.776 2.77788 12.29 2.333Z" fill="black" />
              </svg>
            </a>

            <?php if(RealEstateHelper::isEnabledWishlist()): ?>

            <a href="<?php echo e(route('public.wishlist')); ?>" role="button" class="position-relative">
                <svg width="23" height="20" viewBox="0 0 23 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M12.4338 18.0866C11.6493 18.8948 10.3524 18.896 9.56633 18.0893L6.82495 15.2757L2.68131 10.9892C0.439563 8.62015 0.439563 4.9123 2.68131 2.54328C3.779 1.48831 5.26577 0.937006 6.7859 1.02128C8.30608 1.10555 9.72278 1.81781 10.6971 2.98765L11.0014 3.28628L11.3029 2.97479C12.2773 1.80495 13.694 1.09269 15.2141 1.00842C16.7342 0.924146 18.2211 1.47545 19.3187 2.53042C21.5604 4.89944 21.5604 8.60729 19.3187 10.9763L15.1751 15.2628L12.4338 18.0866Z" stroke="#212329" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                
              </a>
        <?php endif; ?>

          <button class="burger-menu_trigger w-[50px] h-[50px] flex justify-center items-center bg-[#5E2DC2] rounded-[100px]">
            <svg class="burger-menu_icon burger-menu_icon--open burger-menu_icon--active" width="24" height="17" viewBox="0 0 24 17" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M2 2H22M2 8.66664H22M2 15.3333H22" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            </svg>

            <svg class="burger-menu_icon burger-menu_icon--close" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M2 2L16.1421 16.1421M2 16.1421L16.1421 2" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Burger menu -->
  <div id="burger-menu" class="overflow-y-auto relative">
    <div class="px-[20px] py-[10px] flex justify-between items-center gap-[16px] border-b border-b-[#DDDDDD] fixed top-0 left-0 w-full bg-white z-[5]">
      <a class="mobile_logo" href="#">
        <img src="<?php echo e(Theme::asset()->url('images/header-logo2.png')); ?>" alt="" width="100px" height="46px">
      </a>

      <div class="flex items-center gap-[20px]">

        <?php if(RealEstateHelper::isEnabledWishlist()): ?>

        <a href="<?php echo e(route('public.wishlist')); ?>" role="button">
            <svg width="23" height="20" viewBox="0 0 23 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M12.4338 18.0866C11.6493 18.8948 10.3524 18.896 9.56633 18.0893L6.82495 15.2757L2.68131 10.9892C0.439563 8.62015 0.439563 4.9123 2.68131 2.54328C3.779 1.48831 5.26577 0.937006 6.7859 1.02128C8.30608 1.10555 9.72278 1.81781 10.6971 2.98765L11.0014 3.28628L11.3029 2.97479C12.2773 1.80495 13.694 1.09269 15.2141 1.00842C16.7342 0.924146 18.2211 1.47545 19.3187 2.53042C21.5604 4.89944 21.5604 8.60729 19.3187 10.9763L15.1751 15.2628L12.4338 18.0866Z" stroke="#212329" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </svg>
            
          </a>
    <?php endif; ?>

        <button class="burger-menu_trigger w-[50px] h-[50px] flex justify-center items-center bg-[#5E2DC2] rounded-[100px]">
          <svg class="burger-menu_icon burger-menu_icon--open burger-menu_icon--active" width="24" height="17" viewBox="0 0 24 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2 2H22M2 8.66664H22M2 15.3333H22" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
          </svg>

          <svg class="burger-menu_icon burger-menu_icon--close" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2 2L16.1421 16.1421M2 16.1421L16.1421 2" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Menu uplaceholder -->
    <div class="w-full h-[71px]"></div>

    <div class="py-[20px] w-full px-[20px] flex justify-center items-center flex-col gap-[40px]">
      <!-- ! User is not logged in -->


      <?php if(auth('account')->check()): ?>

              <!-- * USER START Logined -->

            <div class="flex flex-col gap-[20px] w-full">
                <ul class="flex flex-col gap-[20px]">
                    <li class="flex justify-center"><a href="<?php echo e(route('public.account.settings')); ?>" role="button" class="text-black text-[25px] font-bold text-center"><?php echo e(__('Profile')); ?></a></li>
                    <li class="flex justify-center"><a href="<?php echo e(route('public.wishlist')); ?>" role="button" class="text-black text-[25px] font-bold text-center"><?php echo e(__('My favorites')); ?></a></li>
                    <li class="flex justify-center"><a href="<?php echo e(route('public.account.properties.index')); ?>" class="text-black text-[25px] font-bold text-center"><?php echo e(__('My listings')); ?></a></li>
                    <li class="flex justify-center"><a href="<?php echo e(route('public.account.logout')); ?>" class="text-black text-[25px] font-bold text-center" onclick="event.preventDefault(); localStorage.setItem('xmetr_logout_success', Date.now().toString()); document.getElementById('logout-form-mob').submit();"><?php echo e(__('Log out')); ?></a></li>
                    <form id="logout-form-mob" style="display: none;" action="<?php echo e(route('public.account.logout')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="redirect_url" value="<?php echo e(url()->current()); ?>">
                    </form>
                </ul>

                <span class="w-full bg-[#DDDDDD] h-[1px] block"></span>
            </div>

            <?php else: ?>

            <div class="flex flex-col gap-[20px] w-[220px]">
                <a href="#modalSignin" role="button" data-bs-toggle="modal" class="px-[24px] py-[15px] w-full border-[2px] !border-[#5E2DC2] rounded-[10px] flex justify-center duration-200 burger-menu_loginBtn">
                  <p class="text-[15px] text-black font-bold duration-200 capitalize"><?php echo e(__('Sign in')); ?></p>
                </a>
                <a href="#modalSignup" role="button" data-bs-toggle="modal" class="px-[24px] py-[15px] w-full border-[2px] bg-[#5E2DC2]/[.10] !border-transparent rounded-[10px] flex justify-center duration-200 burger-menu_registerBtn">
                  <p class="text-[15px] font-bold text-[#5E2DC2] duration-200 capitalize"><?php echo e(__('Sign up')); ?></p>
                </a>
              </div>



            <?php endif; ?>

      <!-- ! User logged in -->


      <?php echo Menu::renderMenuLocation('main-menu', [
        'options' => ['class' => 'ace-responsive-menu', 'data-menu-style'=> 'horizontal'],
        'view' => 'main-menu',
    ]); ?>


      <div class="relative">
        <div class="free-badge absolute top-0 -right-[35px] w-fit"><?php echo e(__('free')); ?></div>
        <a class="ud-btn btn-thm add-property bdrs12 border-0 relative rounded-[100px]" href="<?php echo e(route('public.account.properties.create')); ?>">
          <?php echo e(__('Add listing')); ?>

        </a>
      </div>
    </div>
  </div>





  <!-- Signin Modal -->
  <div class="signup-modal">
    <div class="modal" id="modalSignin" aria-hidden="true" tabindex="-1">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-content">
            <div class="modal-body w-full rounded-[15px] overflow-hidden bg-white h-fit flex flex-col !p-0 !pb-[24px] gap-[20px] relative" style="box-shadow:0px 5px 15px rgba(0, 0, 0, 0.25);">
              <button type="button" class="w-[50px] h-[50px] rounded-[10px] bg-[#212329] flex justify-center items-center hover:bg-[#3e424d] absolute top-[20px] right-[20px]" data-bs-dismiss="modal" aria-label="Close">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M2 2L16.1421 16.1421M2 16.1421L16.1421 2" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
              </button>

              <!-- Header -->
              <div class="w-full flex flex-col items-center gap-[32px] pt-[28px] pb-[20px] px-[30px]" style="background:#FFFFFF;">
                <h3 class="title text-center capitalize"><?php echo e(__('Sign in')); ?></h3>
              </div>

              <div class="px-[20px] flex flex-col gap-[20px]">
                <form action="<?php echo e(route('public.account.login.post')); ?>" method="POST" class="w-full flex flex-col gap-[20px]" id="xmetr-login-form">
                  <?php echo csrf_field(); ?>
                  <input type="hidden" name="intended_url" value="<?php echo e(url()->current()); ?>" id="login-intended-url">
                  <div class="flex flex-col gap-[10px] w-full">
                    <p class="text-black text-[15px] font-bold"><?php echo e(__('Email')); ?> <span class="text-[#FF0000]">*</span></p>

                    <input type="email" name="email"  value="<?php echo e(old('email')); ?>" class="border border-[#DDDDDD] rounded-[10px] px-[16px] py-[14px] <?php if($errors->has('email')): ?> is-invalid <?php endif; ?> " required></input>
                      <?php if($errors->has('email')): ?>
                          <div class="invalid-feedback"><?php echo e($errors->first('email')); ?></div>
                      <?php endif; ?>
                  </div>

                  <div class="flex flex-col gap-[10px] w-full">
                    <p class="text-black text-[15px] font-bold"><?php echo e(__('Password')); ?> <span class="text-[#FF0000]">*</span></p>

                    <input type="password" name="password" class="border border-[#DDDDDD] rounded-[10px] px-[16px] py-[14px] <?php if($errors->has('password')): ?> is-invalid <?php endif; ?>" required></input>
                    <?php if($errors->has('password')): ?>
                      <div class="invalid-feedback"><?php echo e($errors->first('password')); ?></div>
                    <?php endif; ?>
                  </div>

                  <button type="submit" class="w-full bg-[#5E2DC2] px-[20px] py-[12px] duration-200 hover:bg-[#5026a5] rounded-[10px] flex justify-center gap-[10px] relative grow active:scale-[.95]">
                    <p class="text-white text-[15px] font-bold text-center"><?php echo e(__('Log in')); ?></p>
                  </button>
                </form>

                <div>
                  <?php echo apply_filters(BASE_FILTER_AFTER_LOGIN_OR_REGISTER_FORM, null, \Xmetr\RealEstate\Models\Account::class); ?>

                </div>

                

                <?php if(RealEstateHelper::isRegisterEnabled()): ?>
                <p class="text-[#717171] text-[13px] text-center"><?php echo e(__('By clicking create an account, you accept')); ?> <a href="#" target="_blank" class="underline"><?php echo e(__('the terms and conditions')); ?></a> <?php echo e(__('of use of the site')); ?></p>
                <p class="text-[#717171] text-[15px] text-center">
    <a href="#"
       data-bs-toggle="modal"
       data-bs-target="#modalSignup"
       class="text-[#5E2DC2] font-bold">
        <?php echo e(__('Register')); ?>

    </a>
</p>

                <?php endif; ?>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="hiddenbar-body-ovelay"></div>
  </div>



<!-- SignUp Modal -->
  <div class="signup-modal">
    <div class="modal" id="modalSignup" aria-hidden="true" tabindex="-1">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-content">
            <div class="modal-body w-full rounded-[15px] overflow-hidden bg-white h-fit flex flex-col !p-0 !pb-[24px] gap-[20px] relative" style="box-shadow:0px 5px 15px rgba(0, 0, 0, 0.25);">
              <button type="button" class="w-[50px] h-[50px] rounded-[10px] bg-[#212329] flex justify-center items-center hover:bg-[#3e424d] absolute top-[20px] right-[20px]" data-bs-dismiss="modal" aria-label="Close">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M2 2L16.1421 16.1421M2 16.1421L16.1421 2" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
              </button>

              <!-- Header -->
              <div class="w-full flex flex-col items-center gap-[32px] pt-[28px] pb-[20px] px-[30px]" style="background:#FFFFFF;">
                <h3 class="title text-center capitalize"><?php echo e(__('Sign up')); ?></h3>
              </div>

              <?php if(isset($errors) && $errors->has('confirmation')): ?>
              <div class="alert alert-danger">
                  <span><?php echo BaseHelper::clean($errors->first('confirmation')); ?></span>
              </div>
              <br>
          <?php endif; ?>

          <div class="px-[20px] flex flex-col gap-[20px]">
            <form action="<?php echo e(route('public.account.register.post')); ?>" method="POST" class="w-full flex flex-col gap-[20px]" id="xmetr-register-form">
              <?php echo csrf_field(); ?>
              <input type="hidden" name="intended_url" value="<?php echo e(url()->current()); ?>" id="register-intended-url">
              <div class="flex flex-col gap-[10px] w-full">
                <p class="text-black text-[15px] font-bold"><?php echo e(__('Your Name')); ?> <span class="text-[#FF0000]">*</span></p>

                <input type="test" name="first_name"  value="<?php echo e(old('first_name')); ?>" class="border border-[#DDDDDD] rounded-[10px] px-[16px] py-[14px] <?php if($errors->has('first_name')): ?> is-invalid <?php endif; ?> " required></input>
                  <?php if($errors->has('first_name')): ?>
                      <div class="invalid-feedback"><?php echo e($errors->first('first_name')); ?></div>
                  <?php endif; ?>
              </div>
              <div class="flex flex-col gap-[10px] w-full">
                <p class="text-black text-[15px] font-bold"><?php echo e(__('Email')); ?> <span class="text-[#FF0000]">*</span></p>

                <input type="email" name="email"  value="<?php echo e(old('email')); ?>" class="border border-[#DDDDDD] rounded-[10px] px-[16px] py-[14px] <?php if($errors->has('email')): ?> is-invalid <?php endif; ?> " required></input>
                  <?php if($errors->has('email')): ?>
                      <div class="invalid-feedback"><?php echo e($errors->first('email')); ?></div>
                  <?php endif; ?>
              </div>

              <div class="flex flex-col gap-[10px] w-full">
                <p class="text-black text-[15px] font-bold"><?php echo e(__('Password')); ?> <span class="text-[#FF0000]">*</span></p>

                <input type="password" name="password" class="border border-[#DDDDDD] rounded-[10px] px-[16px] py-[14px] <?php if($errors->has('password')): ?> is-invalid <?php endif; ?>" required></input>
                <?php if($errors->has('password')): ?>
                  <div class="invalid-feedback"><?php echo e($errors->first('password')); ?></div>
                <?php endif; ?>
              </div>

              <button type="submit" class="w-full bg-[#5E2DC2] px-[20px] py-[12px] duration-200 hover:bg-[#5026a5] rounded-[10px] flex justify-center gap-[10px] relative grow active:scale-[.95]">
                <p class="text-white text-[15px] font-bold text-center"><?php echo e(__('Sign up')); ?></p>
              </button>
            </form>

            <div>
              <?php echo apply_filters(BASE_FILTER_AFTER_LOGIN_OR_REGISTER_FORM, null, \Xmetr\RealEstate\Models\Account::class); ?>

            </div>

            

            <?php if(RealEstateHelper::isRegisterEnabled()): ?>
            <p class="text-[#717171] text-[13px] text-center"><?php echo e(__('By clicking create an account, you accept')); ?> <a href="#" target="_blank" class="underline"><?php echo e(__('the terms and conditions')); ?></a> <?php echo e(__('of use of the site')); ?></p>
            <p class="text-[#717171] text-[15px] text-center"><a href="#"
       data-bs-toggle="modal"
       data-bs-target="#modalSignin"
       class="text-[#5E2DC2] font-bold"><?php echo e(__('Sign in')); ?></a></p>
            <?php endif; ?>
          </div>
            </div>
          </div>
        </div>
      </div>p
    </div>

    <div class="hiddenbar-body-ovelay"></div>
  </div>

<!-- Select language Modal -->
  <div class="signup-modal">
    <div class="modal" id="modalSelectLang" aria-hidden="true" tabindex="-1">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-content">
            <div class="modal-body w-full rounded-[15px] overflow-hidden bg-white h-fit flex flex-col !p-0 !pb-[24px] gap-[20px] relative" style="box-shadow:0px 5px 15px rgba(0, 0, 0, 0.25);">
              <button type="button" class="w-[50px] h-[50px] rounded-[10px] bg-[#212329] flex justify-center items-center hover:bg-[#3e424d] absolute top-[20px] right-[20px]" data-bs-dismiss="modal" aria-label="Close">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M2 2L16.1421 16.1421M2 16.1421L16.1421 2" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
              </button>

              <!-- Header -->
              <div class="w-full flex flex-col items-center gap-[32px] pt-[28px] pb-[20px] px-[30px]" style="background:#FFFFFF;">
                <h3 class="title text-center"><?php echo e(__('Currency & Language')); ?></h3>
              </div>

              <form action="<?php echo e(route('public.switch-lang-currency')); ?>" method="POST">
                <?php echo csrf_field(); ?>
              <div class="px-[20px] flex flex-col gap-[20px]">
                <?php if(is_plugin_active('language')): ?>
                <?php
                    $supportedLocales = Language::getSupportedLocales();
                    $languageDisplay = setting('language_display', 'all');
                ?>
                <div class="flex flex-col gap-[10px] w-full">
                  <p class="text-black text-[15px] font-bold"><?php echo e(__('Site Language')); ?></p>

                  <div class="h-auto border border-[#DDDDDD] rounded-[10px] pr-[10px] w-full">
                    <select class="x-select w-full h-full pl-[10px] py-[15px]" name="lang">
                        <?php $__currentLoopData = $supportedLocales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $localeCode => $properties): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                <option value="<?php echo e(Language::getSwitcherUrl($localeCode, $properties['lang_code'])); ?>"
                                <?php if($localeCode == Language::getCurrentLocale()): ?> selected <?php endif; ?>
                                >
                                    <?php echo e($properties['lang_name']); ?>

                                </option>

                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                  </div>
                </div>

                <?php endif; ?>

                <?php
                    $currencies = get_all_currencies();
                ?>
                <?php if(count($currencies) > 1): ?>
                <div class="flex flex-col gap-[10px] w-full">
                  <p class="text-black text-[15px] font-bold"><?php echo e(__('Currency')); ?></p>

                  <div class="h-auto border border-[#DDDDDD] rounded-[10px] pr-[10px] w-full">
                    <select class="x-select w-full h-full pl-[10px] py-[15px]" name="currency">
                        <?php $__currentLoopData = $currencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option <?php if(get_application_currency_id() == $currency->id): ?> selected  <?php endif; ?> value="<?php echo e($currency->title); ?>" >
                                <?php echo e($currency->title); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                  </div>
                </div>
                <?php endif; ?>
                <button type="submit" class="bg-[#5E2DC2] rounded-[10px] px-[24px] py-[15px] w-full duration-200 hover:bg-[#5026a5]">
                    <p class="text-white font-bold duration-200 capitalize"><?php echo e(__('save')); ?></p>
                </button>
              </div>
            </form>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="hiddenbar-body-ovelay"></div>
  </div>
  <!-- ! END HEADER BUNDLE -->
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/partials/header.blade.php ENDPATH**/ ?>