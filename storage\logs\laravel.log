[2025-07-19 19:05:49] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: sessions (Connection: sqlite, SQL: select * from "sessions" where "id" = RobK1zu3t1m8xEzREPlVbKwr1yqlht8JLepj3TJp limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: sessions (Connection: sqlite, SQL: select * from \"sessions\" where \"id\" = RobK1zu3t1m8xEzREPlVbKwr1yqlht8JLepj3TJp limit 1) at D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2824): Illuminate\\Database\\Query\\Builder->first(Array)
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('RobK1zu3t1m8xEz...')
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(113): Illuminate\\Session\\DatabaseSessionHandler->read('RobK1zu3t1m8xEz...')
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(101): Illuminate\\Session\\Store->readFromHandler()
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(85): Illuminate\\Session\\Store->loadSession()
#12 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\xmetr\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\xmetr\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: sessions at D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select * from \"...')
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2824): Illuminate\\Database\\Query\\Builder->first(Array)
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('RobK1zu3t1m8xEz...')
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(113): Illuminate\\Session\\DatabaseSessionHandler->read('RobK1zu3t1m8xEz...')
#12 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(101): Illuminate\\Session\\Store->readFromHandler()
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(85): Illuminate\\Session\\Store->loadSession()
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#15 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#18 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\xmetr\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\laragon\\www\\xmetr\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 {main}
"} 
[2025-07-21 15:30:49] local.ERROR: Debugbar exception: SQLSTATE[HY000]: General error: 1 no such table: sessions (Connection: sqlite, SQL: select * from "sessions" where "id" = VRaIxb6A98jriOxWcNksL3qtQTZ1X5nKJO1ooFGs limit 1)  
[2025-08-13 14:59:43] local.ERROR: Debugbar exception: SQLSTATE[HY000]: General error: 1 no such table: sessions (Connection: sqlite, SQL: select * from "sessions" where "id" = ab7tnkWc98gHU7dVH7hcQT7INjhbyeqDFsiDSlwI limit 1)  
[2025-08-13 23:46:59] local.ERROR: Debugbar exception: SQLSTATE[HY000]: General error: 1 no such table: sessions (Connection: sqlite, SQL: select * from "sessions" where "id" = cN0RCcRcBuSgSpcBreW2kzfUoyZsA1q3kT7ysW60 limit 1)  
[2025-08-14 17:00:11] local.ERROR: Debugbar exception: SQLSTATE[HY000]: General error: 1 no such table: sessions (Connection: sqlite, SQL: select * from "sessions" where "id" = R4XxCp58W5xel4cvrBqJTz5NKEQpDUoXxXr5iNon limit 1)  
[2025-08-17 17:58:44] local.ERROR: Debugbar exception: SQLSTATE[HY000]: General error: 1 no such table: sessions (Connection: sqlite, SQL: select * from "sessions" where "id" = By9dK0kVxCawQEF9kAuR4zEukSCWKoiUHBljjbG3 limit 1)  
[2025-08-17 19:37:44] local.ERROR: Debugbar exception: SQLSTATE[HY000]: General error: 1 no such table: sessions (Connection: sqlite, SQL: select * from "sessions" where "id" = jOpIW6V5wYEjR1WQ6C8oaDx5A4lhVQeRnwUPgn63 limit 1)  
[2025-08-17 22:22:28] local.ERROR: Debugbar exception: SQLSTATE[HY000]: General error: 1 no such table: sessions (Connection: sqlite, SQL: select * from "sessions" where "id" = PkceN1bSWxsdY7pTvXpQib63HOAWdcPtu7INdiCb limit 1)  
[2025-08-17 22:51:22] local.ERROR: Debugbar exception: SQLSTATE[HY000]: General error: 1 no such table: sessions (Connection: sqlite, SQL: select * from "sessions" where "id" = Ojo3D0flh6uwaB4LSGF635HQ2bVjN3Kqd90GJPvo limit 1)  
[2025-08-17 22:55:47] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: activations (Connection: sqlite, SQL: delete from "activations" where "completed" = 0 and "created_at" < 2025-08-14 22:55:47) - D:\laragon\www\xmetr\vendor\laravel\framework\src\Illuminate\Database\Connection.php:829  
[2025-08-17 22:55:47] local.ERROR: Debugbar exception: SQLSTATE[HY000]: General error: 1 no such table: sessions (Connection: sqlite, SQL: select * from "sessions" where "id" = 7536ffX9YUlHsWn3hlu70qLeueIqYKTNwTduKhma limit 1)  
[2025-08-17 22:55:48] local.ERROR: Debugbar exception: SQLSTATE[HY000]: General error: 1 no such table: sessions (Connection: sqlite, SQL: select * from "sessions" where "id" = UCDFNoODt9oHLX3PVmp1Mj2wcohnXckntcua7KyC limit 1)  
[2025-08-17 23:07:02] local.ERROR: Debugbar exception: SQLSTATE[HY000]: General error: 1 no such table: sessions (Connection: sqlite, SQL: select * from "sessions" where "id" = F5yBOM0WrifVVSL13kn1HR7jYOuweHypzPdLW9Po limit 1)  
